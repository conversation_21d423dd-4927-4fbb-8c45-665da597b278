<template>
  <div class="fullwidth-element bg-dark dark text-white">

    <div class="container-fluid container-p-y">

      <div class="chart-filters text-center d-none">
        <b-nav pills class="justify-content-around">
          <b-nav-item class="flex-fill"><i class="ion ion-md-browsers d-block"></i> Sessions</b-nav-item>
          <b-nav-item class="flex-fill"><i class="ion ion-md-eye d-block"></i> Leads</b-nav-item>
        </b-nav>
      </div>

      <div class="row">

        <div class="col-md-6 highlight-chart-sessions">
          <b-card no-body class="bg-transparent">
            <b-card-header header-tag="h5" class="border-0 pb-0">
              <span class="card-header-title">Sessions By Source</span>
            </b-card-header>
            <div class="d-flex align-items-center position-relative mt-4 traffic-sources-pie-container">
              <vue-echart :options="pieSessionsOptions" :auto-resize="true"></vue-echart>
            </div>
          </b-card>
        </div>

        <div class="col-md-6 highlight-chart-leads">
          <b-card no-body class="bg-transparent">
            <b-card-header header-tag="h5" class="border-0 pb-0">
              <span class="card-header-title">Total Leads By Source</span>
            </b-card-header>
            <div class="d-flex align-items-center position-relative mt-4 traffic-sources-pie-container">
              <vue-echart :options="pieLeadsOptions" :auto-resize="true"></vue-echart>
            </div>
          </b-card>
        </div>

      </div>

    </div>

  </div>
</template>

<script>
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#1abc9c', '#e67e22', '#3498db', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'traffic-sources-summary',
  props: {
    pieSessionsItems: { type: Array, required: true },
    pieLeadsItems: { type: Array, required: true }
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue'),
    'summary-card': () => import('../summaryCard.vue'),
    'graph-tabs': () => import('../graphTabs.vue')
  },
  computed: {
    pieSessionsOptions () {
      return {
        baseOption: {
          color: colors,
          tooltip: {
            show: true,
            trigger: 'item',
            formatter: (params) => {
              return params.name +
                '<br />' +
                this.$locale.formatNumber(params.value) +
                ` (${params.percent}%)`
            },
            textStyle: {
              fontSize: 13
            }
          },
          legend: {
            data: this.pieSessionsItems.map(x => x.sourcechannelname),
            textStyle: {
              color: 'rgba(255, 255, 255, .9)'
            }
          },
          series: [{
            type: 'pie',
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            data: this.pieSessionsItems.map(x => {
              return {
                value: x.count,
                name: x.sourcechannelname
              }
            }),
            itemStyle: {
              normal: {
                shadowBlur: 50,
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              },
              emphasis: {
                shadowBlur: 50,
                shadowColor: 'rgba(0, 0, 0, 0.3)'
              }
            }
          }]
        },
        media: [
          {
            query: {
              minWidth: 451
            },
            option: {
              legend: {
                show: true,
                y: 'top',
                x: 'left',
                left: 20,
                orient: 'vertical'
              },
              series: [{
                radius: '66%',
                center: ['70%', '40%']
              }]
            }
          },
          {
            query: {
              maxWidth: 450
            },
            option: {
              legend: {
                show: true,
                y: 'bottom',
                x: 'center',
                orient: 'horizontal'
              },
              series: [{
                radius: '53%',
                center: ['50%', '30%']
              }]
            }
          }
        ],
        animationDuration: 2000
      }
    },
    pieLeadsOptions () {
      return {
        baseOption: {
          color: colors,
          tooltip: {
            show: true,
            trigger: 'item',
            formatter: (params) => {
              return params.name +
                  '<br />' +
                  this.$locale.formatNumber(params.value) +
                  ` (${params.percent}%)`
            },
            textStyle: {
              fontSize: 13
            }
          },
          legend: {
            data: this.pieLeadsItems.map(x => x.sourcechannelname),
            textStyle: {
              color: 'rgba(255, 255, 255, .9)'
            }
          },
          series: [{
            type: 'pie',
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            data: this.pieLeadsItems.map(x => {
              return {
                value: x.count,
                name: x.sourcechannelname
              }
            }),
            itemStyle: {
              normal: {
                shadowBlur: 50,
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              },
              emphasis: {
                shadowBlur: 50,
                shadowColor: 'rgba(0, 0, 0, 0.3)'
              }
            }
          }]
        },
        media: [
          {
            query: {
              minWidth: 451
            },
            option: {
              legend: {
                show: true,
                y: 'top',
                x: 'left',
                left: 20,
                orient: 'vertical'
              },
              series: [{
                radius: '66%',
                center: ['70%', '40%']
              }]
            }
          },
          {
            query: {
              maxWidth: 450
            },
            option: {
              legend: {
                show: true,
                y: 'bottom',
                x: 'center',
                orient: 'horizontal'
              },
              series: [{
                radius: '53%',
                center: ['50%', '30%']
              }]
            }
          }
        ],
        animationDuration: 2000
      }
    }
  }
}
</script>

<style scoped>

</style>

<style>
  .traffic-sources-pie-container .echarts {
    height: 300px !important;
  }

  /*please consider with screen width*/
  @media(max-width: 501px) {
    .traffic-sources-pie-container .echarts {
      height: 400px !important;
    }
  }
</style>
