<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
  >
    <template slot="row-details" slot-scope="{ item }">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Form Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.formLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>SMS Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.smsLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Phone Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.phoneLeads) }}</b-col>
        </b-row>
        <b-button size="sm" @click="item.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'remarketing-account-level-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'dateFrom',
          label: 'Date',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.dateAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.dateDesc
        },
        {
          key: 'spend',
          label: 'Spend',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.spendAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.spendDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'impressions',
          label: 'Impressions',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.impressionsAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.impressionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'clicks',
          label: 'Clicks',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.clicksAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.clicksDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'costPerClick',
          label: 'Cost Per Click',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.CPCAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.CPCDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'clickThroughRate',
          label: 'Click Through Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.CTRAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.CTRDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'sessions',
          label: 'Sessions',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.sessionsAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.sessionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'totalLeads',
          label: 'Total Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.totalLeadsAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.totalLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'conversionRate',
          label: 'Conversion Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.convRateAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.convRateDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'costPerLead',
          label: 'Cost Per Lead',
          sortable: true,
          sortTypeAsc: analyticsConstants.remarketingSortTypes.CPLAsc,
          sortTypeDesc: analyticsConstants.remarketingSortTypes.CPLDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'show_details'
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  }
}
</script>

<style scoped>

</style>
