<template>
  <div>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Path & Query:</span>
      <b-form-input v-model="announcementBox.pathAndQuery" slot="payload"></b-form-input>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Title:</span>
      <b-form-input v-model="announcementBox.title" slot="payload"></b-form-input>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Message:</span>
      <b-textarea v-model="announcementBox.message" slot="payload" rows="4"></b-textarea>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Attached Element Id:</span>
      <b-input v-model="announcementBox.element.elementId" placeholder="Leave it empty to display in the center of the window" slot="payload"></b-input>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Tooltip Relative Position:</span>
      <b-form-select v-model="announcementBox.element.tooltipRelativePosition" :options="getTooltipRelativePositionOptions"></b-form-select>
    </detail-row>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'

export default {
  components: { detailRow },
  name: 'announcement-box',
  props: {
    announcementBox: {type: Object, required: true}
  },
  comments: {
    detailRow
  },
  computed: {
    getTooltipRelativePositionOptions () {
      return []
    }
  }
}
</script>

<style>

</style>