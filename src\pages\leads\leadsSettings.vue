<template>
  <b-tabs class="nav-tabs-top nav-responsive-sm" v-model="selectedTab" no-fade>
  <b-tab title="General">
      <div v-if="!isLoading" class="p-4">
        <general-settings @refreshSettings="reload"/>
      </div>
      <div v-else class="my-5">
        <loader size="lg"/>
      </div>
    </b-tab>
    <b-tab v-if="hasLeadsManageDealerSettingsPermissions" title="Turnstile Plugin" lazy>
      <div v-if="!isLoading">
        <turnstile-settings @reload="reload"/>
      </div>
      <div v-else class="my-5">
        <loader size="lg"/>
      </div>
    </b-tab>
    <b-tab v-if="hasLeadsManageCommunicationsPermissions" title="Auto Responders" lazy>
      <div v-if="!isLoading" class="p-4">
        <auto-responders-settings @reload="reload" :items="customEmailTemplates"/>
      </div>
      <div v-else class="my-5">
        <loader size="lg"/>
      </div>
    </b-tab>
  </b-tabs>
</template>

<script>
import generalSettings from '@/components/leads/settings/generalSettings'
import autoRespondersSettings from '@/components/leads/settings/autoRespondersSettings'
import turnstileSettings from '@/components/leads/settings/turnstileSettings'
import loader from '@/components/_shared/loader'
import CustomEmailTemplateService from '@/services/leads/CustomEmailTemplateService'
import permissions from '@/shared/common/permissions'
import { mapGetters } from 'vuex'

export default {
  name: 'leads-settings',
  metaInfo: {
    title: 'Settings'
  },
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      index: 0,
      isLoading: true,
      customEmailTemplates: []
    }
  },
  created () {
    this.populateContent()
  },
  components: {
    'general-settings': generalSettings,
    'auto-responders-settings': autoRespondersSettings,
    'turnstile-settings': turnstileSettings,
    'loader': loader
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasLeadsManageCommunicationsPermissions () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageCommunications)
    },
    hasLeadsManageDealerSettingsPermissions () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageDealerSettings)
    },
    selectedTab: {
      get () {
        return this.index
      },
      set (index) {
        this.index = index
        this.isLoading = true
        this.populateContent()
      }
    }
  },
  methods: {
    populateContent () {
      if (this.index === 0) {
        this.populateAccountSettingsData()
      } else if (this.index === 1) {
        // Turnstile settings tab - no need to populate data here as component handles it
        this.isLoading = false
      } else {
        this.populateCustomEmailTemplatesData()
      }
    },
    reload () {
      this.isLoading = true
      this.populateContent()
    },
    async populateAccountSettingsData () {
      try {
        if (this.hasLeadsManageCommunicationsPermissions) {
          await this.$store.dispatch('leadsAccountSettings/populateAdminAccountSettingsData', this.accountId)
        }
        await this.$store.dispatch('leadsAccountSettings/populateDealerAccountSettingsData', this.accountId)
      } catch (ex) {
        this.$toaster.error('Cannot populate leads account settings model')
        this.$logger.handleError(ex, 'Cannot populate leads account settings model')
      } finally {
        this.isLoading = false
      }
    },
    populateCustomEmailTemplatesData () {
      CustomEmailTemplateService.getCustomEmailTemplates(this.accountId).then(res => {
        this.customEmailTemplates = res.data.templates
      }).catch(ex => {
        this.$toaster.error('Cannot get email templates')
        this.$logger.handleError(ex, 'Cannot get email templates')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
