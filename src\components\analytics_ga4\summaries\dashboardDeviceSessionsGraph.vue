<template>
  <b-card no-body class="mb-3 mb-xl-3 widget-sessions-device">
    <b-card-header header-tag="h5" class="border-0 pb-0">
      <span class="card-header-title">Sessions by Device</span>
    </b-card-header>
    <div class="row">
      <div class="col-md-6 col-xl-12">
        <div class="d-flex align-items-center position-relative mt-3 h-100">
          <vue-echart :options="chartConfigDeviceSessions" :auto-resize="true"></vue-echart>
        </div>
      </div>
    </div>
  </b-card>
</template>

<script>
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'

const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#a7b61a', '#f3e562', '#ff9800', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'dashboard-device-sessions-graph',
  props: {
    mobileCount: { type: Number, required: true },
    desktopCount: { type: Number, required: true }
  },
  components: {
    'vue-echart': () => import('vue-echarts/components/ECharts.vue')
  },
  computed: {
    chartConfigDeviceSessions () {
      return {
        color: colors,
        title: {
          text: 'Pie chart',
          x: 'center'
        },
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: '{b}<br />{c} ({d}%)',
          textStyle: {
            fontSize: 13
          }
        },
        legend: {
          data: ['Desktop', 'Mobile'],
          show: true,
          y: 'top',
          x: 'left',
          left: 20,
          orient: 'vertical',
          textStyle: {
            color: 'rgba(0, 0, 0, .9)'
          }
        },
        series: [{
          name: 'Sessions by Device',
          type: 'pie',
          radius: '75%',
          center: ['50%', '50%'],
          label: {
            normal: {
              show: true,
              position: 'inside',
              formatter: '{b}\n{d}%'
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '18',
                fontWeight: 'bold'
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          data: [
            {
              value: this.mobileCount,
              name: 'Mobile',
              itemStyle: { color: '#007bff' }
            },
            {
              value: this.desktopCount,
              name: 'Desktop',
              itemStyle: { color: '#dc3545' }
            }
          ],
          itemStyle: {
            emphasis: {
              shadowBlur: 50,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }],
        animationDuration: 2000
      }
    }
  }
}
</script>

<style scoped>

</style>
