<template>
  <details-section mode="view" title="OEM Package & Options" @cancel="onCancel" :visible="isVisible" @visibilityChange='onVisibilityChange' canBeHidden disableEdit v-if="isOEMExists">
    <b-row>
      <b-col md="6" v-if="isOEMPackagesExist">
        <packages-section class="pb-0"></packages-section>
      </b-col>
      <b-col md="6" v-if="isOEMOptionsExist">
        <options-section></options-section>
      </b-col>
    </b-row>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import optionsSection from './OptionsSection'
import packagesSection from './PackagesSection'
import detailsSection from '@/components/details/detailsSection'

export default {
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    isVisible () {
      return this.vehicle.isOptionsPackagesTurnedOn
    },
    isOEMOptionsExist () {
      return Object.keys(this.metadata.vehicleOEMOptions || {}).length > 0
    },
    isOEMPackagesExist () {
      return Object.keys(this.metadata.vehicleOEMPackages || {}).length > 0
    },
    isOEMExists () {
      return this.isOEMOptionsExist || this.isOEMPackagesExist
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.vehicle.isOptionsPackagesTurnedOn = val
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'packages-section': packagesSection,
    'options-section': optionsSection,
    'details-section': detailsSection
  }
}
</script>
