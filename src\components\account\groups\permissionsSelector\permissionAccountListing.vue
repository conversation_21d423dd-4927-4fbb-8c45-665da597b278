<template>
  <div v-if="readyToShow" class="permission-account-listing">
    <account-listing
      title="Account Listing"
      :filters="getFilters"
      :listing="listingFiltered"
      @searchChangeLive="applySearch"
      :noPaging="true"
      :searchFullWidth="true"
    >
      <div class="media align-items-center cursor-pointer" slot="accountId" @click="onAccountClick(data.data.accountId)" slot-scope="data">
        <span class="media-body d-block text-dark ml-3">{{data.data.accountId}}</span>
      </div>

      <div class="media align-items-center cursor-pointer" slot="accountName" @click="onAccountClick(data.data.accountId)" slot-scope="data">
        <span class="media-body d-block text-dark ml-3">{{data.data.accountName}}</span>
      </div>
    </account-listing>
  </div>
</template>

<script>
import { ObjectSchema } from '@/shared/common/objectHelpers'
import paging from '@/components/_shared/paging.vue'
import QueryStringHelper from '../../../../shared/common/queryStringHelper'
import accountListingRaw from '../../../_shared/accountListingRaw'
import groupsManagementService from '@/services/accounts/groups/GroupsManagementService'

const defaultValues = new ObjectSchema({
  page: { type: Number, default: 1 },
  pageSize: { type: Number, default: 25 },
  sort: { type: Number, default: 0 },
  search: { type: String, default: '' }
})

let queryHelper = new QueryStringHelper(defaultValues)

export default {
  name: 'permission-account-selector',
  props: ['groupId'],
  components: {
    'paging': paging,
    'account-listing': accountListingRaw
  },
  data () {
    return {
      filters: defaultValues.getObject(),
      listing: null,
      count: 0,
      readyToShow: false
    }
  },
  async mounted () {
    this.filters = queryHelper.parseQueryStringToObject(this.$router)
    await this.populateAccountListing()
    this.readyToShow = true
  },
  computed: {
    getFilters () {
      return {
        ...this.filters,
        count: this.count
      }
    },
    listingFiltered () {
      const searchToLower = this.filters.search.toLowerCase()
      return this.listing.filter(x =>
        x.accountId.toString().toLowerCase().includes(searchToLower) ||
        x.accountName.toLowerCase().includes(searchToLower)
      )
    }
  },
  methods: {
    async populateAccountListing () {
      let res = await groupsManagementService.getGroup(this.groupId)

      this.listing = res.data.accounts.map(x => ({
        accountId: x.accountId,
        accountName: x.dealershipName
      }))
      this.count = this.listing.length
    },
    applySearch (search) {
      this.filters.page = 1
      this.filters.search = search
      this.populateAccountListing()
    },
    pageChanged (newPage) {
      this.filters.page = newPage
      this.populateAccountListing()
    },
    changePageSize (newSize) {
      this.filters.pageSize = newSize
      this.filters.page = 1
      this.populateAccountListing()
    },
    onAccountClick (accountId) {
      this.$emit('accountClick', accountId)
    }
  }
}
</script>

<style>
  .permission-account-listing .account-listing .table-holder {
    max-height: 55vh;
    overflow-y: scroll;
  }
</style>
