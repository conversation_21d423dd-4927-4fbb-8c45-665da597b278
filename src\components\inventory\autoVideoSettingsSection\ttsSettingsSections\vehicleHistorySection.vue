<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Vehicle History" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Vehicle History Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomVehicleHistorySettings"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomVehicleHistorySettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomVehicleHistorySettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Carfax One Owner" :rules="getAccountCarfaxOneOwnerRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Carfax One Owner
              <b-icon variant="secondary" :id="`carfax-one-owner-popover-${id}`" icon="question-circle"></b-icon>:
              <b-popover :target="`carfax-one-owner-popover-${id}`" triggers="hover click blur">
                <template #title>Available Placeholders</template>
                <b-list-group>
                  <b-list-group-item @click="copyText('{VehicleModel}')">{VehicleModel} <b-icon icon="files"></b-icon></b-list-group-item>
                </b-list-group>
              </b-popover>
            </span>
            <span slot="payload" v-if="isViewMode">{{ settings.carfaxOneOwnerDescriptionText || '-' }}</span>
            <b-form-input slot="payload" v-else v-model="updatedSettings.carfaxOneOwnerDescriptionText" :disabled="isDisabled"></b-form-input>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-vehicle-history-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      id: this.$uuid.v4(),
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  computed: {
    getAccountCarfaxOneOwnerRules () {
      if (this.accountLevel && this.updatedSettings.hasToUseCustomVehicleHistorySettings) {
        return 'required'
      }
      return ''
    }
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    copyText (text) {
      this.$copyProvider.copyTextToClipboard(text).then(() => {
        this.$toaster.success(`Copied the text: ${text}`, { timeout: 4000 })
      }).catch(err => {
        console.error(err)
      })
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
