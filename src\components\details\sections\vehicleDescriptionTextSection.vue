<template>
  <details-section title="Vehicle Description Text" @cancel="onCancel" class="vehicle-overview-text" v-model="mode">
    <div class="view mt-3" v-if="mode === 'view'">
      <template v-if="hasToUseTabs">
        <b-tabs class="nav-tabs-top" no-fade>

          <b-tab title="Website Description" active>
            <b-card no-body class="border-0">
              <b-card-body>

                <detail-row titlePosition="start" v-if="hasToShowAutoDescriptionText">
                  <span slot="title">Auto Description Text:</span>
                  <span slot="payload">{{vehicle.galleryIntroDescription.autoDescriptionText}}</span>
                </detail-row>

                <detail-row titlePosition="start" v-if="hasToShowAutoDescriptionText">
                  <span slot="title">Display Auto Description:</span>
                  <span slot="payload">{{getBooleanDescription(useAutogeneratedText)}}</span>
                </detail-row>

                <detail-row titlePosition="start">
                  <span slot="title">Description Text:</span>
                  <span slot="payload">{{vehicle.galleryIntroDescription.customDescriptionText}}</span>
                </detail-row>

              </b-card-body>
            </b-card>
          </b-tab>

          <b-tab title="eBay Description">
            <b-card no-body class="border-0">
              <b-card-body>

                <detail-row titlePosition="start">
                  <span slot="title">Description Text:</span>
                  <span slot="payload">{{vehicle.eBayIntroDescription.customDescriptionText}}</span>
                </detail-row>

              </b-card-body>
            </b-card>

          </b-tab>

        </b-tabs>
      </template>
      <template v-else>
        <detail-row>
          <span slot="title">Description Text:</span>
          <span slot="payload">{{vehicle.galleryIntroDescription.customDescriptionText}}</span>
        </detail-row>
      </template>
    </div>

    <div class="edit mt-3" v-else-if="mode === 'edit'">
      <template v-if="hasToUseTabs">
        <b-tabs class="nav-tabs-top" no-fade>

          <b-tab title="Website Description" active @click="changeTab(false)">
            <b-card no-body class="border-0">
              <b-card-body>

                <detail-row titlePosition="start" v-if="hasToShowAutoDescriptionText" editMode mobileWrap>
                  <span slot="title">Auto Description Text:</span>
                  <b-form-textarea :value="vehicle.galleryIntroDescription.autoDescriptionText" rows="12" slot="payload" disabled></b-form-textarea>
                </detail-row>

                <detail-row titlePosition="start" v-if="hasToShowAutoDescriptionText">
                  <span slot="title">Display Auto Description:</span>
                  <b-form-checkbox v-model="useAutogeneratedText" slot="payload">{{getBooleanDescription(useAutogeneratedText)}}</b-form-checkbox>
                </detail-row>
                <ValidationProvider rules="min:0|xml|encodeXml" name="Description Text" v-slot="{errors}">
                  <detail-row titlePosition="start" editMode mobileWrap :error="errors[0]">
                    <span slot="title">Description Text:</span>
                    <quill-editor
                      slot="payload"
                      :options="options"
                      class="w-100"
                      v-model="vehicle.galleryIntroDescription.customDescriptionText"
                    />
                  </detail-row>
                </ValidationProvider>

              </b-card-body>
            </b-card>
          </b-tab>

          <b-tab title="eBay Description" @click="changeTab(true)">
            <b-card no-body class="border-0">

              <b-card-body>
                <ValidationProvider rules="min:0|xml|encodeXml" name="eBay Description" v-slot="{errors}">
                  <detail-row titlePosition="start" editMode mobileWrap :error="errors[0]">
                    <span slot="title">Description Text:</span>
                    <quill-editor
                      slot="payload"
                      :options="options"
                      class="w-100"
                      v-model="vehicle.eBayIntroDescription.customDescriptionText">
                    </quill-editor>
                  </detail-row>
                </ValidationProvider>
              </b-card-body>
            </b-card>

          </b-tab>

        </b-tabs>
      </template>
      <template v-else>
        <ValidationProvider rules="min:0|xml|encodeXml" name="Description Text" v-slot="{errors}">
          <detail-row titlePosition="start" editMode mobileWrap :error="errors[0]">
            <span slot="title">Description Text:</span>
            <quill-editor slot="payload" :options="options" class="w-100" v-model="vehicle.galleryIntroDescription.customDescriptionText"></quill-editor>
          </detail-row>
        </ValidationProvider>
      </template>
    </div>
  </details-section>
</template>

<style src="@/vendor/libs/vue-quill-editor/typography.scss" lang="scss"></style>
<style src="@/vendor/libs/vue-quill-editor/editor.scss" lang="scss"></style>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      isEbayTabEnabled: false,
      mode: 'view',
      options: {
        modules: {
          toolbar: [
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'color': [] }, { 'background': [] }],
            ['blockquote'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }]
          ]
        }
      }
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'accountSettings']),
    hasToUseTabs () {
      return !!this.vehicle.eBayIntroDescription
    },
    hasToShowAutoDescriptionText () {
      return this.accountSettings.marketingSettings && this.accountSettings.marketingSettings.isAutomaticTextGeneratorActive === true
    },
    useAutogeneratedText: {
      get () {
        return !this.vehicle.galleryIntroDescription.hasCustomDescription
      },
      set (val) {
        this.vehicle.galleryIntroDescription.hasCustomDescription = !val
      }
    }
  },
  methods: {
    changeTab (value) {
      this.isEbayTabEnabled = value
    },
    getBooleanDescription (boolValue) {
      return boolValue ? 'Yes' : 'No'
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow,
    'quill-editor': () => import('vue-quill-editor/dist/vue-quill-editor').then(m => m.quillEditor).catch(() => {})
  }
}
</script>

<style scoped lang="scss">
</style>

<style lang="scss">
  .vehicle-overview-text .detail-row .detail-row__payload > div:first-child {
    width: 100%;
  }
</style>
