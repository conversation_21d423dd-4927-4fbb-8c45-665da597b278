<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Summary" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Sections Ordering:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomSectionsOrdering"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomSectionsOrdering ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomSectionsOrdering)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <detail-row :fixed-payload-width="true" :title-position="'start'">
          <span slot="title">Sections' Order:</span>
          <div slot="payload" class="d-flex flex-row">
            <draggable
              class="col p-0"
              v-bind="draggableOptions"
              v-model="updatedSettings.orderedSectionItems"
              @start="isSectionDragging = true"
              @end="isSectionDragging = false"
              :move="isMoveAllowed"
              :class="{on: isSectionDragging, off: !isSectionDragging}"
              :handle="`.ordered-sections-container`"
            >
              <b-form-group class="ordered-sections-container" @click="sectionIndex = index" v-for="(section, index) in updatedSettings.orderedSectionItems" :key="index">
                <div class="d-flex flex-row align-middle">
                  <i class="ion ion-ios-menu m-0 opacity-100 mr-2"></i>
                  <b-form-checkbox v-model="section.isEnabled" :disabled="isSectionDisabled(section.section)">{{ getSectionDesc(section.section) }}</b-form-checkbox>
                </div>
                <input type="text" hidden :value="index+1" @change="onSectionIndexChanged($event, index + 1)"/>
              </b-form-group>
            </draggable>
          </div>
        </detail-row>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import draggable from 'vuedraggable'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'

const descriptionTextSections = videoEncoderTypes.descriptionTextSections

export default {
  name: 'tts-summary-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true,
      isSectionDragging: true,
      draggableOptions: {
        animation: 150
      }
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper,
    draggable
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    getSectionDesc (value) {
      let section = Object.values(descriptionTextSections).find(x => x.value === value)
      return (section || {text: '-'}).text
    },
    onSectionIndexChanged (newVal, oldVal) {
      if (!Number.isInteger(+newVal.target.value)) {
        return
      }

      let newIndex = (+newVal.target.value) - 1
      let oldIndex = oldVal - 1
      let arrayItem = this.updatedSettings.orderedSectionItems[oldIndex]
      this.updatedSettings.orderedSectionItems.splice(oldIndex, 1)
      this.updatedSettings.orderedSectionItems.splice(newIndex, 0, arrayItem)
    },
    isMoveAllowed (event) {
      let firstSection = this.updatedSettings.orderedSectionItems[event.draggedContext.index]
      let secondSection = this.updatedSettings.orderedSectionItems[event.draggedContext.futureIndex]
      return !(this.isSectionDisabled(firstSection.section) | this.isSectionDisabled(secondSection.section))
    },
    isSectionDisabled (section) {
      if (this.isDisabled | this.isViewMode) {
        return true
      }
      return section === descriptionTextSections.intro.value || section === descriptionTextSections.outro.value
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
