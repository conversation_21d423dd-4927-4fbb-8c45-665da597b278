<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Payment Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Full Payment Required In:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.FullPaymentRequiredInDays" :options="getDaysOptions"></b-form-select>
          <span v-else>{{settings.FullPaymentRequiredInDays}} days</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Number of days that Full Payment from the eBay Buyer is required in.
          </b-form-text>
        </b-form-group>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'
import globals from '../../../globals'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  computed: {
    getDaysOptions () {
      return [
        {value: 3, text: '3 Days'},
        {value: 7, text: '7 Days'},
        {value: 10, text: '10 Days'},
        {value: 14, text: '14 Days'}
      ]
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateAccountPaymentSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Payment Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay automated payment settings', this.settingsToUpdate)
      }).finally(() => {
        this.isViewMode = true
        this.isUpdatingProcessed = false
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
</style>
