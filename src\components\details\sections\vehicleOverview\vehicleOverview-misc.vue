<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Sub Model / Trim" :text="vehicle.trim"/>

    <auto-detail-row title="Engine Details" :text="vehicle.engine"/>

    <auto-detail-row title="Transmission" :text="getTransmissionDescription"/>

    <auto-detail-row :title="getVehicleOverviewDescription.exportCategory.name" :text="getVehicleOverviewDescription.exportCategory.value" />

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="Year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     name="year"
                     :options="getYearsOptions"
                     @change="onInputYear"/>
    </detail-row>
    </ValidationProvider>
    <ValidationProvider immediate name="Make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make,inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"/>
    </detail-row>
    </ValidationProvider>

    <auto-detail-row title="Model" v-model="vehicle.model" validation-rule="required|max:50|xml"/>

    <auto-detail-row title="Sub Model / Trim" v-model="vehicle.trim" validation-rule="max:100|xml"/>

    <auto-detail-row title="Engine" v-model="vehicle.engine" validation-rule="max:50|xml"/>

    <auto-detail-row title="Transmission" v-model="vehicle.transTypeId" :options="getTransOptions"/>

    <auto-detail-row :title="getVehicleOverviewDescription.exportCategory.name" v-model="getVehicleOverviewDescription.exportCategory.value" :options="getNameValueOptions(getVehicleOverviewDescription.exportCategory.nameValueOptions)"/>

  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import autoDetailRow from '../../helpers/autoDetailRow'

export default {
  name: 'vehicle-overview-misc',
  props: {
    mode: String
  },
  computed: {
    ...mapGetters('details', [
      'vehicle',
      'metadata',
      'miscDetails'
    ]),
    ...mapGetters('categoryData', [
      'makes',
      'years'
    ]),
    getVehicleOverviewDescription () {
      return {
        exportCategory: this.miscDetails.features.find(x => x.id === -11001)
      }
    },
    getTransmissionDescription () {
      return this.metadata.transmissionOptions[this.vehicle.transTypeId] || '-'
    },
    isModelSelected () {
      return !!this.vehicle.model
    },
    getYearsOptions () {
      return this.years.map(x => ({
        value: x,
        text: x
      }))
    },
    getMakesOptions () {
      return this.makes.map(x => ({
        value: x,
        text: x
      }))
    },
    getTransOptions () {
      return {
        0: 'Select',
        ...this.metadata.transmissionOptions
      }
    },
    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    }
  },
  methods: {
    getNameValueOptions (nameValueOptions) {
      return nameValueOptions.map(x => ({
        value: x.value,
        text: x.key
      }))
    },
    onInputYear (newVal) {
      this.vehicle.year = newVal.isInputMode ? newVal.text : newVal.value
    },
    onInputMakes (newVal) {
      this.vehicle.make = newVal.isInputMode ? newVal.text : newVal.value
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>
