<template>
  <div class="split-helper">
    <span class='message' v-for="(i, index) in messages" :key="index">
      {{i}}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    messages: Array
  }
}
</script>

<style scoped>
  .split-helper {
    display: flex;
    flex-wrap: wrap;
  }

  .message:not(:first-child):before {
    content: "|";
    padding: 0 5px;
  }

  @media (max-width:576px) {
    .split-helper .message {
      width: 100%;
    }
    .split-helper .message:before {
      content: none;
      padding: 0;
    }
  }
</style>
