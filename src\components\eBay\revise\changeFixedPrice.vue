<template>
<ValidationObserver ref="validator">
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{reviseHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Current Fixed Price:</span>
      <span slot="payload">{{getPriceDesc(revise.StartPrice)}}</span>
    </detail-row>
    <ValidationProvider name="New Fixed Price" :rules="{is_not: `${revise.StartPrice}`, required: true}" v-slot="{errors}">
    <detail-row bootstrapMode title-position="start" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">New Fixed Price:</span>
      <b-form-input slot="payload" name="New_Fixed_Price" v-model="changeFixedPriceData.NewFixedPrice" type="number"></b-form-input>
    </detail-row>
    </ValidationProvider>
    <ValidationProvider name="Auto Decline Amount" :rules="`max_value: ${changeFixedPriceData.NewFixedPrice - 1}`" v-slot="{errors}">
    <detail-row bootstrapMode title-position="start" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">Auto Decline Amount:</span>
      <b-form-input slot="payload" name="Auto_Decline_Amount" v-model="changeFixedPriceData.AutoDeclineAmount" type="number"></b-form-input>
    </detail-row>
    </ValidationProvider>
  </b-card>
</ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import loader from '@/components/_shared/loader'
import {mapGetters} from 'vuex'
import globals from '@/globals'
import numeral from 'numeral'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      changeFixedPriceData: {
        NewFixedPrice: 0,
        AutoDeclineAmount: 0
      },
      isDisabled: false
    }
  },
  components: {
    detailRow,
    loader
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  mounted () {
    this.initData()
  },
  methods: {
    onConfirm () {
      this.$refs.validator.validate().then(res => {
        if (res) {
          this.isDisabled = true
          let apiParams = {
            accountId: this.revise.AccountId,
            auctionId: this.revise.AuctionId,
            data: this.changeFixedPriceData
          }

          this.$store.dispatch('eBayRevise/changeFixedPrice', apiParams).then(res => {
            this.$toaster.success('Changed Fixed Price Successfully')
          }).catch(ex => {
            this.$toaster.exception(ex, 'Something went wrong!')
            if (ex.response && ex.response.status !== 400) {
              this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
            }
          }).finally(() => {
            this.isDisabled = false
            setTimeout(() => this.$router.go(), 4000)
          })
        }
      })
    },
    getPriceDesc (value) {
      return numeral(value).format('$0,0')
    },
    initData () {
      this.changeFixedPriceData = {
        NewFixedPrice: globals().getClonedValue(this.revise.StartPrice),
        AutoDeclineAmount: globals().getClonedValue(this.revise.BestOfferAutoDeclinePrice)
      }
    }
  }
}
</script>
