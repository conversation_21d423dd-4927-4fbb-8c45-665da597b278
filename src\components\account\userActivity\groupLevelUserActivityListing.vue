<template>
  <b-table
    :items="items"
    :fields="getTableFields"
    :sort-by="tableSortBy"
    :sort-desc="tableSortDesc"
    @sort-changed="onSortChanged"
    :no-sort-reset="true"
    :no-local-sorting="true"
    hover
    striped
    responsive="sm"
    show-empty
  >
    <template #empty>
      <div class="text-center">
        <span class="text-muted">No Items Found</span>
      </div>
    </template>
    <template #cell(manage)="data">
      <b-btn size="sm" @click="onShowDetails(data)">{{ data.detailsShowing ? 'Hide' : 'Show' }} Details</b-btn>
      <a class="btn btn-outline-secondary btn-sm" :href='getLogDetailsPath(data.item.id)' target="_blank">
        <i class="ion ion-md-open"></i>
      </a>
    </template>
    <template #row-details="data">
      <b-card>
        <log-node
          v-if='data.item.nodes'
          :data="data.item.nodes"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
      </b-card>
    </template>
  </b-table>
</template>

<script>
import userActivityConstants from '../../../shared/accounts/userActivityConstants'
import AccountUserActivityService from '../../../services/accounts/AccountUserActivityService'
import {userTypes} from '@/shared/users/constants'
import logNode from '../../_shared/logItemNode.vue'
import moment from 'moment'

export default {
  props: {
    items: {type: Array, default: () => []},
    sortType: {type: Number, required: true}
  },
  components: {
    'log-node': logNode
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'requestInformation.userName',
          label: 'User Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivityConstants.sortTypes.userNameAsc,
          sortTypeDesc: userActivityConstants.sortTypes.userNameDesc
        },
        {
          key: 'requestInformation.user.userType',
          label: 'User Type',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivityConstants.sortTypes.userTypeAsc,
          sortTypeDesc: userActivityConstants.sortTypes.userTypeDesc,
          formatter: val => (Object.values(userTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'groupName',
          label: 'Group Name',
          sortable: true,
          sortTypeAsc: userActivityConstants.sortTypes.groupNameAsc,
          sortTypeDesc: userActivityConstants.sortTypes.groupNameDesc,
          tdClass: 'py-2 align-middle',
          formatter: val => val || '-'
        },
        {
          key: 'action',
          label: 'Action Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivityConstants.sortTypes.operationResultAsc,
          sortTypeDesc: userActivityConstants.sortTypes.operationResultDesc,
          formatter: val => (Object.values(userActivityConstants.groupActionTypes).find(x => x.value === val) || {text: '-'}).text
        },
        {
          key: 'startProcessingDateTime',
          label: 'Date',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: userActivityConstants.sortTypes.dateTimeAsc,
          sortTypeDesc: userActivityConstants.sortTypes.dateTimeDesc,
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm A')
        },
        {
          key: 'manage',
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    },
    onShowDetails (data) {
      if (data.item.nodes) {
        data.toggleDetails()
      } else {
        AccountUserActivityService.getUserActivityDetails(data.item.id, userActivityConstants.userActivityTypes.group.value).then(res => {
          data.item.nodes = { nodes: res.data.details }
          data.toggleDetails()
        }).catch(ex => {
          this.$toaster.error('Failed on getting user activity details')
          this.$logger.handleError(ex, 'Exception occurred on api call', { params: {logId: data.item.id} })
        })
      }
    },
    getLogDetailsPath (id) {
      return `/accounts/useractivity/${userActivityConstants.userActivityTypes.group.value}/${id}/details`
    }
  }
}
</script>
