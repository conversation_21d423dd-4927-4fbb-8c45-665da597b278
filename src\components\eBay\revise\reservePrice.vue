<template>
<ValidationObserver ref="validator">
  <b-card>
    <b-row class="border-bottom mb-2 py-2">
      <b-col>
        <strong>{{reviseHeader}}</strong>
      </b-col>
      <c-button v-if="!isDisabled" :message="`Are you sure you want ${btnDesc}?`" variant="primary" size="sm" @confirm="onConfirm">
        {{btnDesc}}
      </c-button>
      <loader class="mr-5" v-else size="sm"/>
    </b-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link slot="payload" :href="getListingUrl" class="text-info"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Bid Status:</span>
      <span slot="payload">
        {{revise.BidsTotal > 0 ? revise.BidsTotal : 'No Bids'}}<span v-if="revise.BidsTotal > 0">(<b-link :href="getViewBidsUrl" class="text-info"><u>View Bid History</u></b-link>)</span>
      </span>
    </detail-row>
    <detail-row bootstrapMode :fixed-payload-width="true">
      <span slot="title">Current Reserve Price:</span>
      <span slot="payload">{{getReservePrice}}</span>
    </detail-row>
    <ValidationProvider name="New Reserve Price" :rules="getValidateRule" v-slot="{errors}">
    <detail-row bootstrapMode title-position="start" v-if="!isRemovePrice" :fixed-payload-width="true" :error="errors[0]">
      <span slot="title">New Reserve Price:</span>
      <b-form-input slot="payload" name="New_Reserve_Price" type="number" v-model="reservePriceData.NewReservePrice"></b-form-input>
    </detail-row>
    </ValidationProvider>
  </b-card>
</ValidationObserver>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import numeral from 'numeral'
import constants from '@/shared/ebay/constants'
import {mapGetters} from 'vuex'
import loader from '@/components/_shared/loader'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      reservePriceData: {
        NewReservePrice: null
      },
      isDisabled: false
    }
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getReservePrice () {
      if (this.revise.ReservePrice > 0) {
        return numeral(this.revise.ReservePrice).format('$0,0')
      }

      return 'No Reserve Price'
    },
    getViewBidsUrl () {
      return constants.eBayInfoUrls.ebayViewBids(this.revise.AuctionId)
    },
    getValidateRule () {
      if (this.type === constants.reviseOptions.addReservePrice.key) {
        return 'required|min_value:1' + this.revise.BinPrice > 0 ? `|max_value: ${this.revise.BinPrice - 1}` : ''
      }
      if (this.type === constants.reviseOptions.lowerReservePrice.key) {
        return `required|min_value:1|max_value:${this.revise.ReservePrice - 1}`
      }
      if (this.type === constants.reviseOptions.changeReservePrice.key) {
        let rule = {min_value: 1, is_not: `${this.revise.ReservePrice}`, required: true}
        if (this.revise.BinPrice > 0) {
          rule.max_value = this.revise.BinPrice - 1
        }
        return rule
      }
      return ''
    },
    isRemovePrice () {
      return this.type === constants.reviseOptions.removeReservePrice.key
    },
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  components: {
    detailRow,
    loader
  },
  methods: {
    onConfirm () {
      if (!this.validate()) {
        return
      }
      let apiParams = {
        accountId: this.revise.AccountId,
        auctionId: this.revise.AuctionId,
        data: this.reservePriceData
      }
      switch (this.type) {
        case constants.reviseOptions.addReservePrice.key:
          return this.addReservePrice(apiParams)
        case constants.reviseOptions.lowerReservePrice.key:
          return this.lowerReservePrice(apiParams)
        case constants.reviseOptions.changeReservePrice.key:
          return this.changeReservePrice(apiParams)
        case constants.reviseOptions.removeReservePrice.key:
          return this.removeReservePrice(apiParams)
      }
    },
    async validate () {
      if (this.isRemovePrice) {
        return true
      }

      let res = await this.$refs.validator.validate()

      return res
    },
    addReservePrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/addReservePrice', apiParams).then(res => {
        this.$toaster.success('Added Reserve Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    lowerReservePrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/lowerReservePrice', apiParams).then(res => {
        this.$toaster.success('Lowered Reserve Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    changeReservePrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/changeReservePrice', apiParams).then(res => {
        this.$toaster.success('Changed Reserve Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    },
    removeReservePrice (apiParams) {
      this.isDisabled = true
      this.$store.dispatch('eBayRevise/removeReservePrice', apiParams).then(res => {
        this.$toaster.success('Removed Reserve Price Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isDisabled = false
        setTimeout(() => this.$router.go(), 4000)
      })
    }
  }
}
</script>
