<template>
  <div>
    <b-table
      :fields='getTableFields'
      :items='items'
      :sort-by="tableSortBy"
      :sort-desc="tableSortDesc"
      @sort-changed="onSortChanged"
      :bordered="false"
      :no-sort-reset="true"
      :no-local-sorting="true"
      striped
      hover
      responsive
      tbody-class='py-2 align-middle'
    >
      <template #cell(date)="data">
        {{getDateTimeFormat(data.item)}}
      </template>
      <template #cell(statisticStatus)="data">
        {{getStatisticStatus(data.item.status)}}
      </template>
      <template #cell(statisticType)="data">
        {{getStatisticType(data.item.type)}}
      </template>
      <template #cell(communicationType)="data">
        {{getCommunicationType(data.item)}}
      </template>
      <template #cell(number)="data">
        {{getServiceLogNumber(data.item)}}
      </template>
      <template #cell(operationType)="data">
        {{getSynchronizationTypeDesc(data.item.operationType)}}
      </template>
      <template #cell(resultStatus)="data">
        {{getResultStatusDesc(data.item)}}
      </template>
      <template #cell(spamScanStatus)="data">
          <i class="ion ion-ios-close text-danger zoomeds" style="font-size:2rem; display: flex; justify-content: center;" v-if="data.item.spamScanStatus === spamScanStatus.caught"></i>
          <i class="ion ion-ios-checkmark text-success zoomeds" style="font-size:2rem; display: flex; justify-content: center;" v-if="data.item.spamScanStatus === spamScanStatus.passed"></i>
      </template>
      <template #cell(turnstileValidationStatus)="data">
        {{getTurnstileValidationStatusDesc(data.item.turnstileValidationStatus)}}
      </template>
      <template #cell(manage)="data">
        <b-btn size="sm" @click="onShowDetails(data.item)">Details</b-btn>
      </template>
    </b-table>
    <b-modal
    v-if='modalData'
    :visible='isShowModal'
    @hide='hideModal'
    size="lg">
      <template #modal-header>
        <router-link :to="{ name: 'leads-details-log', params: {logType: getLeadsLogType(), logId: modalData.rootNode.logId} }"><h4 class="text-primary">Leads Details Log</h4></router-link>
      </template>
      <b-card>
        <log-node
          :data="modalData"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
      </b-card>
      <template #modal-footer>
        <b-btn variant="secondary" size="md" @click="hideModal">Close</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import { logTypes, processingStatusType } from '@/shared/leads/common'
import { statisticTypes, communicationTypes, synchronizationOperationTypes, logsSortType, leadsNotificationStatus, spamScanStatus, turnstileValidationStatusTypes } from '@/shared/leads/logs/logsCommon'
import LeadsLogService from '@/services/logs/LeadsLogService'
import moment from 'moment'

export default {
  name: 'leads-log-listing',
  props: {
    logType: { type: Number, default: 0 },
    items: { type: Array, required: true },
    sort: { type: Number, required: true }
  },
  data () {
    return {
      logTypes,
      spamScanStatus,
      isShowModal: false,
      modalData: null
    }
  },
  components: {
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  computed: {
    getTableFields () {
      switch (this.logType) {
        case this.logTypes.voice.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'phoneFrom', label: 'Number From', tdClass: 'py-2 align-middle'},
            {key: 'phoneTo', label: 'Number To', tdClass: 'py-2 align-middle'},
            {key: 'proxyPhoneFrom', label: 'Proxy Number From', tdClass: 'py-2 align-middle'},
            {key: 'proxyPhoneTo', label: 'Proxy Number To', tdClass: 'py-2 align-middle'},
            {key: 'description', label: 'Description', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.sms.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'phoneFrom', label: 'Number From', tdClass: 'py-2 align-middle'},
            {key: 'phoneTo', label: 'Number To', tdClass: 'py-2 align-middle'},
            {key: 'proxyPhoneFrom', label: 'Proxy Number From', tdClass: 'py-2 align-middle'},
            {key: 'proxyPhoneTo', label: 'Proxy Number To', tdClass: 'py-2 align-middle'},
            {key: 'description', label: 'Description', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.serviceLog.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'number', label: 'Number', tdClass: 'py-2 align-middle'},
            {key: 'description', label: 'Description', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.email.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'emailFrom', label: 'Email From', tdClass: 'py-2 align-middle'},
            {key: 'emailTo', label: 'Email To', tdClass: 'py-2 align-middle'},
            {key: 'proxyEmailTo', label: 'Proxy Email To', tdClass: 'py-2 align-middle'},
            {key: 'description', label: 'Description', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.gallery.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'emailFrom', label: 'Email', tdClass: 'py-2 align-middle'},
            {key: 'phoneNumberFrom', label: 'Phone Number', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle', sortable: true, sortTypeAsc: logsSortType.dateAsc, sortTypeDesc: logsSortType.dateDesc},
            {key: 'spamScanStatus', label: 'Passed Spam Filter', tdClass: 'py-2 align-middle'},
            {key: 'turnstileValidationStatus', label: 'Turnstile Validation Status', tdClass: 'py-2 align-middle'},
            {key: 'leadSourceIp', label: 'Source IP', tdClass: 'py-2 align-middle', sortable: true, sortTypeAsc: logsSortType.sourceIpAsc, sortTypeDesc: logsSortType.sourceIpDesc},
            {key: 'classCSubnet', label: 'Subnet', tdClass: 'py-2 align-middle'},
            {key: 'urlsInBody', label: 'URLs In Lead Body', tdClass: 'py-2 align-middle'},
            {key: 'emailsInBody', label: 'Emails In Lead Body', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.leadsNotification.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'phoneFrom', label: 'Number From', tdClass: 'py-2 align-middle'},
            {key: 'phoneTo', label: 'Number To', tdClass: 'py-2 align-middle'},
            {key: 'proxyPhoneFrom', label: 'Proxy Number From', tdClass: 'py-2 align-middle'},
            {key: 'proxyPhoneTo', label: 'Proxy Number To', tdClass: 'py-2 align-middle'},
            {key: 'emailFrom', label: 'Email From', tdClass: 'py-2 align-middle'},
            {key: 'communicationType', label: 'Communication Type', tdClass: 'py-2 align-middle'},
            {key: 'resultStatus', label: 'Result Status', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.eBay.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'eBayUserEmail', label: 'Email', tdClass: 'py-2 align-middle'},
            {key: 'eBayUserId', label: 'UserId', tdClass: 'py-2 align-middle'},
            {key: 'description', label: 'Description', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.synchronization.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'operationType', label: 'Operation Type', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        case this.logTypes.statistic.key:
          return [
            {key: 'accountId', label: 'Account Id', tdClass: 'py-2 align-middle'},
            {key: 'statisticStatus', label: 'Status', tdClass: 'py-2 align-middle'},
            {key: 'statisticType', label: 'Type', tdClass: 'py-2 align-middle'},
            {key: 'date', label: 'Date', tdClass: 'py-2 align-middle'},
            {key: 'manage', label: 'Manage', tdClass: 'py-2 align-middle'}
          ]
        default:
          return ''
      }
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sort || x.sortTypeDesc === this.sort)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sort
      } else {
        return false
      }
    }
  },
  methods: {
    onShowDetails (item) {
      LeadsLogService.getLeadsApiDetailsLog(this.getLeadsLogType(), item.id).then(result => {
        this.isShowModal = true
        this.modalData = result.data
      }).catch(ex => {
        this.$toaster.exception(ex, `Failed on getting details of the log`, {timeout: 5000})
      })
    },
    hideModal () {
      this.modalData = null
      this.isShowModal = false
    },
    getLeadsLogType () {
      switch (this.logType) {
        case this.logTypes.voice.key:
          return this.logTypes.voice.type
        case this.logTypes.sms.key:
          return this.logTypes.sms.type
        case this.logTypes.serviceLog.key:
          return this.logTypes.serviceLog.type
        case this.logTypes.email.key:
          return this.logTypes.email.type
        case this.logTypes.gallery.key:
          return this.logTypes.gallery.type
        case this.logTypes.leadsNotification.key:
          return this.logTypes.leadsNotification.type
        case this.logTypes.eBay.key:
          return this.logTypes.eBay.type
        case this.logTypes.synchronization.key:
          return this.logTypes.synchronization.type
        case this.logTypes.statistic.key:
          return this.logTypes.statistic.type
        default:
          return -1
      }
    },
    getDateTimeFormat (item) {
      if (item.startProcessingDateTime) {
        return moment(item.startProcessingDateTime).format('MM/DD/YYYY hh:mm:ss A')
      } else if (item.dateTimeCreated) {
        return moment(item.dateTimeCreated).format('MM/DD/YYYY hh:mm:ss A')
      }

      return ''
    },
    getStatisticStatus (status) {
      let res = processingStatusType.find(x => x.key === status)

      if (res) {
        return res.label
      }

      return ''
    },
    getStatisticType (type) {
      let res = statisticTypes.find(x => x.key === type)

      if (res) {
        return res.label
      }

      return ''
    },
    getCommunicationType (item) {
      let type = ((item.technicalInformation || {}).communicationType || {}).data
      let res = communicationTypes.find(x => x.key === type)

      if (res) {
        return res.label
      }

      return ''
    },
    getServiceLogNumber (item) {
      let number = ''
      if (item.closedConversation && item.closedConversation.data && item.closedConversation.data.userProxyPhone) {
        number = item.closedConversation.data.userProxyPhone.phoneNumber
      }
      if (item.twilioPhonePriceUpdated && item.twilioPhonePriceUpdated.data) {
        number = item.twilioPhonePriceUpdated.data.phoneNumber
      }
      if (item.removedCommunication && item.removedCommunication.data && item.removedCommunication.data.proxyPhone) {
        number = item.removedCommunication.data.proxyPhone.phoneNumber
      }

      return number
    },
    getSynchronizationTypeDesc (type) {
      let res = synchronizationOperationTypes.find(x => x.key === type)

      if (res) {
        return res.label
      }

      return ''
    },
    getResultStatusDesc (item) {
      let status = ((item.technicalInformation || {}).receivedResultStatus || {}).data
      let res = leadsNotificationStatus.find(x => x.key === status)

      if (res) {
        return res.label
      }

      return ''
    },
    getTurnstileValidationStatusDesc (status) {
      let res = turnstileValidationStatusTypes.find(x => x.key === status)

      if (res) {
        return res.label
      }

      return ''
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.$emit('sortChange', sortingColumn.sortTypeDesc)
      } else {
        this.$emit('sortChange', sortingColumn.sortTypeAsc)
      }
    }
  }
}
</script>
