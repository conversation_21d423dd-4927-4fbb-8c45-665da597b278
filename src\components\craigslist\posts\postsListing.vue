<template>
  <div v-if="isLoaded && totalItems > 0">
    <b-card no-body>
      <div class="table-responsive">
        <b-table
          :items="tableItems"
          :sort-by="tableSortBy"
          :sort-desc="tableSortDesc"
          @sort-changed="onSortChanged"
          :fields="tableFields"
          :striped="true"
          :bordered="false"
          :no-sort-reset="true"
          :no-local-sorting="true"
          responsive
        >
          <template #cell(title)="row">
            <div class="media align-items-center">
              <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="row.item.craigsListPostFirstPhotoUrl">
              <span>{{row.item.craigsListTitle}}</span>
            </div>
          </template>

          <template #cell(show_details)="row">
            <div class="media align-items-center">
              <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
                {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
              </b-button>
            </div>
          </template>
          <template #row-details="row">
            <b-card>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Stock#:</span>
                </b-col>
                <b-col>
                  {{row.item.craigsListVehicleStockNumber}}
                </b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>VIN:</span>
                </b-col>
                <b-col>
                  {{row.item.craigsListVin}}
                </b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Miles:</span>
                </b-col>
                <b-col>
                  {{geMilageFormat(row.item.craigsListVehicleMileage)}}
                </b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Engine:</span>
                </b-col>
                <b-col>
                  {{row.item.craigsListVehicleEngineDescription}}
                </b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Transmission:</span>
                </b-col>
                <b-col>
                  {{row.item.craigsListVehicleTransmission}}
                </b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Ext. Color:</span>
                </b-col>
                <b-col>
                  {{row.item.craigsListVehicleExteriorColor}}
                </b-col>
              </b-row>
              <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Int. Color:</span>
                </b-col>
                <b-col>
                  {{row.item.craigsListVehicleInteriorColor}}
                </b-col>
              </b-row>
                <b-row class="mb-2">
                <b-col sm="12" md="4" lg="3" xl="2">
                  <span>Days In Stock:</span>
                </b-col>
                <b-col>
                  {{getDaysInStock(row.item.inStockDate)}}
                </b-col>
              </b-row>
            </b-card>
          </template>

          <template #cell(status)="row">
            <span v-if='status !== 1' class='text-danger'>{{getStatus(row.item)}}</span>
            <span v-else>{{getStatus(row.item)}}</span>
          </template>

          <template #cell(manage)="row">
            <template>
              <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
                <template slot="button-content">
                  <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
                </template>
                <b-dropdown-item v-if="row.item.craigsListPostViewLink" :href="row.item.craigsListPostViewLink" target="_blanck">View Post</b-dropdown-item>
                <b-dropdown-item v-if="isPostingAllowed" :to="getPostPath(row)">Repost</b-dropdown-item>
                <b-dropdown-item v-if="hasCraigslistFullAccess" :href="row.item.craigslistManagePostUrl" target="_blanck">Manage</b-dropdown-item>
              </b-dropdown>
            </template>
          </template>
        </b-table>
      </div>

      <!-- / Pagination -->
      <paging
        :pageNumber="pageNumber"
        :pageSize="pageSize"
        :totalItems="totalItems"
        titled
        pageSizeSelector
        @numberChanged="onPageNumberChanged"
        @changePageSize="onPageSizeChanged"
      />
    </b-card>
  </div>
  <div class="ml-3" v-else-if="!isLoaded">
    Loading...
  </div>
  <div class="ml-3" v-else>
    No posts found
  </div>
</template>

<script>
import permission from '@/shared/common/permissions'
import Constants from '@/shared/craigslist/constants'
import moment from 'moment'
import numeral from 'numeral'
import CraigslistDescriptionHelper from '@/shared/craigslist/craigslistDescriptionHelper'
import postsFiltersMixin from '@/mixins/craigslist/postsFilterMixin'
import {mapGetters} from 'vuex'

const craigslistDescriptionHelper = new CraigslistDescriptionHelper()

export default {
  name: 'posts-listing',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, required: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true },
    status: { type: Number, required: true },
    isPostingAllowed: { type: Boolean, required: true },
    isLoaded: { type: Boolean, required: true }
  },
  data () {
    return {
    }
  },
  components: {
    'paging': () => import('@/components/_shared/paging')
  },
  mixins: [postsFiltersMixin],
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.sortType = sortingColumn.sortTypeDesc
      } else {
        this.sortType = sortingColumn.sortTypeAsc
      }
      this.$emit('update:sortType', this.sortType)
      this.filtersChanged()
    },
    getPostPath (data) {
      return `/craigslist/${data.item.accountID}/post/${data.item.craigsListVin}/?postingtype=2`
    },
    geMilageFormat (value) {
      return numeral(value).format('0,0')
    },
    getDaysInStock (value) {
      const now = moment()
      return now.diff(moment(value), 'days')
    },
    getStatus (data) {
      if (this.status === 1) {
        return 'Active'
      } else {
        return 'Deleted ' + moment(data.dateTimeDeactivated).format('MM/DD/YYYY')
      }
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user
    },
    hasCraigslistFullAccess () {
      if (this.user) {
        return this.user.hasPermissions && this.user.hasPermissions(permission.CraigslistFullAccess)
      } else {
        return false
      }
    },
    tableFields () {
      return [
        {
          key: 'title',
          label: 'Vehicle',
          sortable: false,
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          label: '',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'status',
          label: 'Status',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'craigsListVehiclePrice',
          sortable: true,
          sortTypeAsc: Constants.craigslistAdsSortType.postedPriceAsc,
          sortTypeDesc: Constants.craigslistAdsSortType.postedPriceDesc,
          label: 'Posted Price',
          tdClass: 'py-2 align-middle',
          formatter: value => numeral(value).format('$0,0')
        },
        {
          key: 'craigsListPostAreaDescription',
          sortable: true,
          sortTypeAsc: Constants.craigslistAdsSortType.postingAreaAsc,
          sortTypeDesc: Constants.craigslistAdsSortType.postingAreaDesc,
          label: 'Posting Area',
          tdClass: 'py-2 align-middle',
          formatter: value => value ? value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') : 'Empty'
        },
        {
          key: 'dateTimePosted',
          sortable: true,
          sortTypeAsc: Constants.craigslistAdsSortType.postedDateAsc,
          sortTypeDesc: Constants.craigslistAdsSortType.postedDateDesc,
          label: 'Posted Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY')
        },
        {
          key: 'craigsListPostExpirationDate',
          sortable: true,
          sortTypeAsc: Constants.craigslistAdsSortType.expDateAsc,
          sortTypeDesc: Constants.craigslistAdsSortType.expDateDesc,
          label: 'Exp. Date',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY')
        },
        {
          key: 'postingSource',
          sortable: false,
          label: 'Source',
          tdClass: 'py-2 align-middle',
          formatter: value => craigslistDescriptionHelper.getCraigslistSourceDescription(value)
        },
        {
          key: 'manage',
          label: 'Manage',
          sortable: false,
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  }
}
</script>
