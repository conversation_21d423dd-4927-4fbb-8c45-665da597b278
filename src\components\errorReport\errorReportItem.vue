<template>
  <div>
    <div v-if="reportCopy">
      <div v-if="reportCopy.applicationReports">
        <span class="tittle">Errors Report : ({{dateRange[0]}} - {{dateRange[1]}})</span>
        <template v-if="hasUserFullAccess">
          <b-btn v-if="!isEditable" variant="secondary" size="sm" class="float-right" @click="editReport()">
            <font-awesome-icon icon="pencil-alt"/>
            <span class="btn-title">Edit</span>
          </b-btn>
          <b-btn v-if="isEditable" variant="primary" size="sm" class="float-right d-inline" @click="saveReport()">
            <font-awesome-icon icon="cloud-upload-alt" />
            <span class="btn-title">Save</span>
          </b-btn>
          <b-btn v-if="isEditable" variant="secondary" size="sm" class="float-right mr-1 d-inline" @click="cancelEditReport()">
            <span class="btn-title">Cancel</span>
          </b-btn>
        </template>
        <br><br>
        <span v-for="applicationReport in reportCopy.applicationReports">ApplicationName: <span class="white-space">{{applicationReport.applicationName}}</span><br>
          <span>Errors count: {{applicationReport.errorsCount}}</span><br>
          <span>Errors:</span><br>
          <span v-for="groupedErrorInfo in applicationReport.groupedErrorsInfoList">
            <span class="error-subject">- {{groupedErrorInfo.rule}}</span><br>
            <span class="error-block">Count: {{groupedErrorInfo.errorsCount}}</span><br>
            <span class="error-block">
              Machine name: <span v-if="groupedErrorInfo.machineErrorList.length > 0 " class="machine-first">{{groupedErrorInfo.machineErrorList[0].machineName}} (Count: {{groupedErrorInfo.machineErrorList[0].count}})</span>
              <div v-for="(machineError, index) in groupedErrorInfo.machineErrorList">
                <span v-if="index !== 0 " class="machine">{{machineError.machineName}} (Count: {{machineError.count}}) </span>
              </div>
            </span>
            <span class="error-block example-error-link" @click="viewExampleErrorClicked(groupedErrorInfo.exampleId)" @click.middle="viewExampleErrorMiddleClicked(groupedErrorInfo.exampleId)">View example error</span><br>
            <div class="error-block">
              <div v-if="isEditable">
                <span>Comment:<b-input class="comment-input" size="sm" v-model="groupedErrorInfo.comment" placeholder="Comment"></b-input></span>
              </div>
              <div v-else>
                <span>Comment: {{groupedErrorInfo.comment}}</span>
              </div>
            </div>
            <br>
          </span>
        </span>
        <span v-if="reportCopy.notRecognizedErrors || reportCopy.collisions" class="tittle">Total identified: {{reportCopy.recognizedErrorsCount}} errors</span><br><br>
      </div>
      <div v-if="reportCopy.notRecognizedErrors">
        <span class="error-tittle">Not recognized errors:</span><br>
        <span class="error-subject" v-for="notRecognizedError in reportCopy.notRecognizedErrors">
          - <router-link :to="{name: 'error-details', params: {errorId: notRecognizedError.errorId}}" target='_blank'>{{notRecognizedError.errorId}}</router-link> :
          {{notRecognizedError.subject}}
          <br>
        </span><br>
      </div>
      <div v-if="reportCopy.collisions">
        <span class="error-tittle">Collisions:</span><br>
        <span class="error-block" v-for="collision in reportCopy.collisions">
          <router-link :to="{name: 'error-details', params: {errorId: collision.errorId}}" target='_blank'>{{collision.errorId}}</router-link>
          <span v-for="rule in collision.rules">{{' -> '+ rule}}</span>
          <br>
        </span><br>
      </div>
      <span v-if="reportCopy.total > 0" class="tittle">Total: {{reportCopy.total}} errors</span>
      <span v-else class="tittle">There are no errors</span>
    </div>
  </div>
</template>

<script>

import moment from 'moment-timezone'
import globals from '@/globals'
import {errorMonitorTimezone} from '@/shared/errorReport/constants'

export default {
  name: 'error-reportCopy',
  props: {
    value: { type: Object },
    hasUserFullAccess: Boolean
  },
  created () {
    this.reportCopy = globals().getClonedValue(this.value)
  },
  data () {
    return {
      editState: false,
      reportCopy: {}
    }
  },
  computed: {
    isEditable () {
      return this.editState
    },
    dateRange () {
      let dateFrom = moment(this.reportCopy.dateFrom).tz(errorMonitorTimezone).format('MM/DD/YYYY HH:mm')
      let dateTo = moment(this.reportCopy.dateTo).tz(errorMonitorTimezone).format('MM/DD/YYYY HH:mm')
      return [dateFrom, dateTo]
    }
  },
  methods: {
    errorMonitorLink (id) {
      this.$router.push({name: 'error-details', params: { errorId: id }})
    },
    cancelEditReport () {
      this.editState = false
      this.$emit('editReport', this.editState)
      this.reportCopy = globals().getClonedValue(this.value)
    },
    editReport () {
      this.editState = true
      this.$emit('editReport', this.editState)
    },
    saveReport () {
      this.editState = false
      this.$store.dispatch('systemTools/updateErrorReport', this.reportCopy).then(res => {
        this.$emit('saveReport', this.reportCopy)
        this.$toaster.success('Error Report Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Error occurred on update error reportCopy', this.reportCopy)
      })
    },
    viewExampleErrorClicked (exampleId) {
      this.$emit('viewExampleErrorClicked', exampleId)
    },
    viewExampleErrorMiddleClicked (exampleId) {
      this.$emit('viewExampleErrorMiddleClicked', exampleId)
    }
  },
  watch: {
    value () {
      this.reportCopy = globals().getClonedValue(this.value)
    }
  }
}
</script>

<style>
.inline {
  display: inline-block;
}
.comment-input {
  margin-left: 5px;
  display: inline-block;
  width: 530px;
}
.ion-ios-add-circle {
  font-size: 20px;
  cursor: pointer;
}
.tittle {
  font-size: 16px;
  font-weight: bold;
}
.error-subject {
  margin-left: 30px;
}
.error-block {
  margin-left: 37px;
}
.white-space {
  margin-left: 5px;
}
.machine-first {
  margin-left: 10px;
}
.machine {
  margin-left: 148px;
}
.error-tittle {
  font-size: 16px;
  font-weight: bold;
  color: red;
}
.example-error-link {
  color: #1e70cd;
  text-decoration: none;
  background-color: transparent;
  cursor: pointer;
}
.example-error-link:hover {
  color: #3c8ae2;
  text-decoration: none;
}
</style>
