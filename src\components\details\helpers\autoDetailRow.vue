<template>
<ValidationProvider slim immediate :rules="validationRule" :name="title" v-slot="{errors}">
  <detail-row fixedPayloadWidth :editMode="!isReadonly" :error="errors[0]" :titlePosition="titlePosition">
    <span slot="title">{{title}}:</span>
    <spliter-helper slot="payload" v-if="hasToSplitValue" :messages="text"></spliter-helper>
    <span slot="payload" v-else-if="isReadonly">{{getText}}</span>
    <b-form-input :name="title" slot="payload" v-else-if="!options" :value="value" @input="onInput" />
    <custom-select
      v-else-if="enableCustom && !!options"
      slot="payload"
      :name="title"
      v-model="value"
      @input="onInput"
      :options="options"
      :customSelectValue="{selectVal: value,inputVal: value}"
    />
    <b-form-select slot="payload" v-else-if="!!options" :value="value" :options="options" @input="onInput" :disabled='isDisabled'/>
  </detail-row>
</ValidationProvider>
</template>

<script>
import detailRow from './detailRow'
import spliterHelper from './spliterHelper'
import selectWithCustomValue from './selectWithCustomValue'

export default {
  name: 'auto-detail-row',
  props: {
    title: String,
    value: [String, Number, Object, Boolean],
    text: {
      type: [String, Number, Object, Boolean, Array],
      default () {
        return undefined
      }
    },
    options: [Object, Array],
    validationRule: String,
    titlePosition: String,
    disabled: Boolean,
    enableCustom: Boolean
  },
  computed: {
    isDisabled () {
      if (this.disabled) {
        return true
      }
      return false
    },
    getText () {
      return ['', null, undefined].includes(this.text) ? '-' : this.text
    },
    isReadonly () {
      return !(this.text === undefined)
    },
    hasToSplitValue () {
      return this.isReadonly && Array.isArray(this.text)
    }
  },
  methods: {
    onInput (newValue) {
      this.$emit('input', newValue)
    }
  },
  components: {
    'spliter-helper': spliterHelper,
    'detail-row': detailRow,
    'custom-select': selectWithCustomValue
  }
}
</script>
