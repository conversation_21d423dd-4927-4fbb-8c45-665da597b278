<template>
  <div class="row mt-3">

    <div class="col" v-if="totalItems > 0">

      <b-card no-body>
        <div class="table-responsive">
          <b-table
            striped
            hover
            :items="tableItems"
            :fields="tableFields"
            :no-sort-reset="true"
            :no-local-sorting="isPaginated"
            :sort-by="tableSortBy"
            :sort-desc="tableSortDesc"
            @sort-changed="onSortChanged"
            class="card-table"
          >
            <template #cell(account)="{ value :{ accountName, accountId, isAccessAllowed }}">
              <account-name-link :disabled="!isAccessAllowed" @click="onAccountNameClicked(accountName, accountId)">{{ accountName }} ({{ accountId}})</account-name-link>
            </template>
            <template #cell(show_details)="row">
              <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
                {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
              </b-button>
            </template>
            <template #row-details="row">
              <slot name="row-details" :item="row"></slot>
            </template>
            <template #cell(absoluteUrl)="data">
              <b-button variant="default btn-xs icon-btn md-btn-flat" :href="data.item.absoluteUrl" v-b-tooltip.hover :title="data.item.absoluteUrl">
                <i class="ion ion-md-eye"></i>
              </b-button>
            </template>
          </b-table>
        </div>

        <!-- / Pagination -->
        <paging
          v-if="isPaginated"
          :pageNumber="pageNumber"
          :pageSize="pageSize"
          :totalItems="totalItems"
          titled
          pageSizeSelector
          @numberChanged="onPageNumberChanged"
          @changePageSize="onPageSizeChanged"
        />
      </b-card>

    </div>
    <div class="col" v-else>
      No reports found
    </div>

  </div>
</template>

<script>
export default {
  name: 'common-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, required: true },
    tableFields: { type: Array, required: true },
    isPaginated: { type: Boolean, required: true },
    sortType: { type: Number, required: true },
    pageNumber: { type: Number, default: 0 },
    pageSize: { type: Number, default: 0 }
  },
  components: {
    'paging': () => import('../../../components/_shared/paging.vue'),
    'account-name-link': () => import('../accountNameLink')
  },
  computed: {
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)

      this.$emit('update:sortType', sortingColumn
        ? value.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : 0)
    },
    onPageNumberChanged (newVal) {
      this.$emit('update:pageNumber', newVal)
    },
    onPageSizeChanged (newPageSize) {
      this.$emit('update:pageSize', newPageSize)
    },
    onAccountNameClicked (accountName, accountId) {
      this.$emit('accountNameClicked', { accountName: accountName, accountId: accountId })
    }
  }
}
</script>

<style scoped>

</style>
