<template>
  <router-link :to="newTo"><slot></slot></router-link>
</template>

<script>
import {mapGetters} from 'vuex'
import globals from '@/globals'

export default {
  name: 'analytics-router-link',
  props: {
    to: null
  },
  computed: {
    ...mapGetters('analyticsGa4', ['dateRangeQuery']),
    newTo () {
      const newTo = globals().getClonedValue(this.to)
      if (!newTo.query || !newTo.query.datefrom) {
        newTo.query = {
          ...newTo.query,
          ...this.dateRangeQuery
        }
      }
      return newTo
    }
  }
}
</script>

<style scoped>

</style>
