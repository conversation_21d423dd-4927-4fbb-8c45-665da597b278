<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Deposit Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Require a Deposit:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsRequiredDeposit"  :options="getRequiredDepositOptions"></b-form-select>
          <span v-else>{{getRequiredDepositDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
           Deposits are paid via PayPal (Business or Premier PayPal; account required)
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title"></span>
        <b-form-group
          slot="payload"
        >
          <b-form-checkbox v-model="settingsToUpdate.IsRequiredImmediateDeposit" class="custom-text-nowrap" :disabled="isViewMode">Require Immediate Deposit (max of $500) for Buy It Now Items.</b-form-checkbox>
          <b-form-text class="text-dark custom-text-nowrap">
            Using Immediate Deposit will set your Sell To Locations to United States Only.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title">Deposit Amount:</span>
        <price-input v-if="!isViewMode" v-model="settingsToUpdate.DepositAmount" slot="payload" active></price-input>
        <span v-else slot="payload">{{settings.DepositAmount}}$</span>
      </detail-row>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title">Deposit Required Within:</span>
        <div class="d-flex flex-row w-100" slot="payload">
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HoursToDeposit" class="mr-2" :options="getDepositRequiredWithinOptions"></b-form-select>
          <span v-else>{{settings.HoursToDeposit}} hours</span>
          <span class="align-self-center custom-text-nowrap ml-1">of auction / listing close</span>
        </div>
      </detail-row>
      <detail-row v-if="settingsToUpdate.IsRequiredDeposit" :large-payload-width="true">
        <span slot="title">Change Deposit Per Listing</span>
        <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsAllowedChangeDepositPerListing" slot="payload" :options="getChangeDepositPerListingOptions"></b-form-select>
        <span v-else slot="payload">{{getChangeDepositPerListingDesc}}</span>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import ebayOptions from '@/shared/ebay/ebayOptions'
import priceInput from '@/components/_shared/priceInput'
import globals from '../../../globals'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow,
    priceInput
  },
  mixins: [editSettingsMixin],
  computed: {
    getRequiredDepositOptions () {
      return ebayOptions.yesNoOptions
    },
    getDepositRequiredWithinOptions () {
      return ebayOptions.depositHoursOptions
    },
    getChangeDepositPerListingOptions () {
      return ebayOptions.allowNotAllowOptions
    },
    getRequiredDepositDesc () {
      return (ebayOptions.yesNoOptions.find(x => x.value === this.settings.IsRequiredDeposit) || {text: '-'}).text
    },
    getChangeDepositPerListingDesc () {
      return (ebayOptions.allowNotAllowOptions.find(x => x.value === this.settings.IsAllowedChangeDepositPerListing) || {text: '-'}).text
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateDepositSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Deposit Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay automated deposit settings', this.settingsToUpdate)
      }).finally(() => {
        this.isViewMode = true
        this.isUpdatingProcessed = false
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
.custom-input-width {
  width: 5rem;
}
</style>
