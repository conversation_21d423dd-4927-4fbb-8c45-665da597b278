<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Vehicle Mileage" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Mileage Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomMileageSettings"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomMileageSettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomMileageSettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider name="Mileage Text Template" :rules="getAccountMileageTextTemplateRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">Mileage Text Template
              <b-icon variant="secondary" :id="`mileage-text-description-popover-${id}`" icon="question-circle"></b-icon>:
              <b-popover :target="`mileage-text-description-popover-${id}`" triggers="hover click blur">
                <template #title>Available Placeholders</template>
                <b-list-group>
                  <b-list-group-item @click="copyText('{VehicleModel}')">{VehicleModel} <b-icon icon="files"></b-icon></b-list-group-item>
                  <b-list-group-item @click="copyText('{MileageDescription}')">{MileageDescription} <b-icon icon="files"></b-icon></b-list-group-item>
                </b-list-group>
              </b-popover>
            </span>
            <span slot="payload" v-if="isViewMode">{{ settings.mileageTextTemplate || '-'}}</span>
            <b-form-input slot="payload" v-else v-model="updatedSettings.mileageTextTemplate" :disabled="isDisabled"></b-form-input>
          </detail-row>
        </ValidationProvider>
        <ValidationProvider name="No Mileage Text Template" :rules="getAccountNoMileageTextTemplateRules" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
              <span slot="title">No Mileage Text Template
                <b-icon variant="secondary" :id="`no-mileage-description-popover-${id}`" icon="question-circle"></b-icon>:
                <b-popover :target="`no-mileage-description-popover-${id}`" triggers="hover click blur">
                  <template #title>Available Placeholders</template>
                  <b-list-group>
                    <b-list-group-item @click="copyText('{VehicleModel}')">{VehicleModel} <b-icon icon="files"></b-icon></b-list-group-item>
                    <b-list-group-item @click="copyText('{RunUnits}')">{RunUnits} <b-icon icon="files"></b-icon></b-list-group-item>
                  </b-list-group>
                </b-popover>
              </span>
              <span slot="payload" v-if="isViewMode">{{ settings.noMileageTextTemplate || '-' }}</span>
              <b-form-input slot="payload" v-else v-model="updatedSettings.noMileageTextTemplate" :disabled="isDisabled"></b-form-input>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'

export default {
  name: 'tts-vehicle-mileage-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      id: this.$uuid.v4(),
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  computed: {
    hasToApplyAccountValidationRules () {
      return this.accountLevel && this.updatedSettings.hasToUseCustomMileageSettings
    },
    getAccountMileageTextTemplateRules () {
      return this.hasToApplyAccountValidationRules ? 'required' : ''
    },
    getAccountNoMileageTextTemplateRules () {
      return this.hasToApplyAccountValidationRules ? 'required' : ''
    }
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    copyText (text) {
      this.$copyProvider.copyTextToClipboard(text).then(() => {
        this.$toaster.success(`Copied the text: ${text}`, { timeout: 4000 })
      }).catch(err => {
        console.error(err)
      })
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
