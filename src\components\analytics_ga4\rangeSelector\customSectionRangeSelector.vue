<template>
  <div>
    <b-dropdown right boundary="window" :text="label" variant="primary btn-round" class="d-block" size="md">
      <div role="group" class="ddown-menu p-3" style="width:280px;">
        <range-form
          :year="year"
          :quarter="quarter"
          :month="month"
          @submited="notifyAboutChanges"
        />
      </div>
    </b-dropdown>
  </div>
</template>

<script>
export default {
  props: {
    year: Number,
    quarter: Number,
    month: Number,
    label: String
  },
  components: {
    'range-form': () => import('./rangeForm.vue')
  },
  methods: {
    notifyAboutChanges (newRange) {
      this.$emit('changed', newRange)
    }
  }
}
</script>
