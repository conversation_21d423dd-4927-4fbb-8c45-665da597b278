<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Auction Listings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <ValidationProvider name="Listing Quantity" rules="max_value:100|min_value:0" v-slot="{errors}">
      <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Listing Quantity:</span>
        <div slot="payload" class="d-flex flex-row w-100">
          <b-form-input v-if="!isViewMode" name="Listing_Quantity" class="custom-input-width" @blur.native="calculateListingQuantity" min="0" v-model="settingsToUpdate.ListingQuantityInPercent" type="number"></b-form-input>
          <span v-else slot="payload">{{settings.ListingQuantityInPercent}}</span>
          <span class="ml-2 align-self-center custom-text-nowrap">% of all Listings are Auctions.</span>
        </div>
      </detail-row>
      </ValidationProvider>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Exclude New Vehicles:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasToExcludeNewVehicles" :options="getExcludeNewVehiclesOptions"></b-form-select>
          <span v-else>{{getExcludeNewVehiclesDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
           If this checkbox is selected, even if "New" is selected above as a condition eligible for listing, New vehicles will NEVER be launched as National Auctions.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Listing Duration:</span>
        <b-form-select v-if="!isViewMode" slot="payload" v-model="settingsToUpdate.ListingDurationInDays" :options="getDurationOptions"></b-form-select>
        <span v-else slot="payload">{{settings.ListingDurationInDays || '-'}} days</span>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Best Offers:</span>
        <b-form-select slot="payload" v-if="!isViewMode" v-model="settingsToUpdate.HasToAllowedBestOffer" :options="getBestOfferOptions"></b-form-select>
        <span v-else slot="payload">{{getBestOfferDesc}}</span>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Starting Price($):</span>
        <price-input v-if="!isViewMode" v-model="settingsToUpdate.StartingPrice" active slot="payload"></price-input>
        <span v-else slot="payload">{{settings.StartingPrice}}$</span>
      </detail-row>
      <ValidationProvider name="Reserve Price" rules="max_value:100|min_value:0" v-slot="{errors}">
      <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Reserve Price:</span>
        <div slot="payload" class="d-flex flex-row w-100">
          <b-form-input v-if="!isViewMode" name="Reserve_Price" class="custom-input-width" min="0" v-model="settingsToUpdate.ReservePriceInPercent" type="number"></b-form-input>
          <span v-else>{{settings.ReservePriceInPercent}}</span>
          <span class="ml-2 align-self-center custom-text-nowrap">% of the lowest price.</span>
        </div>
      </detail-row>
      </ValidationProvider>
      <ValidationProvider name="Buy It Now Price" rules="max_value:100|min_value:0" v-slot="{errors}">
      <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Buy It Now Price:</span>
        <div slot="payload" class="d-flex flex-row w-100">
          <b-form-input v-if="!isViewMode" name="Buy_It_Now_Price" class="custom-input-width" min="0" v-model="settingsToUpdate.BuyItNowPriceInPercent" type="number"></b-form-input>
          <span v-else>{{settings.BuyItNowPriceInPercent}}</span>
          <span class="ml-2 align-self-center custom-text-nowrap">% of the lowest price.(Use zero for no BIN Price)</span>
        </div>
      </detail-row>
      </ValidationProvider>
    </template>
  </editSettingsHelper>
</template>

<script>
import globals from '@/globals'
import priceInput from '@/components/_shared/priceInput'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import ebayOptions from '@/shared/ebay/ebayOptions'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow,
    priceInput
  },
  mixins: [editSettingsMixin],
  computed: {
    getExcludeNewVehiclesOptions () {
      return [{value: true, text: 'Yes, Exclude New Vehicles from Auctions'}, {value: false, text: 'No, Do Not Exclude New Vehicles from Auctions'}]
    },
    getDurationOptions () {
      return ebayOptions.automatedDurationOptions
    },
    getBestOfferOptions () {
      return ebayOptions.bestOfferOptions
    },
    getExcludeNewVehiclesDesc () {
      return (this.getExcludeNewVehiclesOptions.find(x => x.value === this.settings.HasToExcludeNewVehicles) || {text: '-'}).text
    },
    getBestOfferDesc () {
      return (ebayOptions.bestOfferOptions.find(x => x.value === this.settings.HasToAllowedBestOffer) || {text: '-'}).text
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$emit('update', this.settingsToUpdate)
      this.isUpdatingProcessed = false
      this.isViewMode = true
    },
    copySettings () {
      this.settingsToUpdate = globals().getClonedValue(this.settings)
    },
    calculateListingQuantity () {
      this.$emit('calculateListingQuantity', +this.settingsToUpdate.ListingQuantityInPercent)
    }
  },
  watch: {
    'settings': {
      deep: true,
      handler: function () {
        this.copySettings()
      }
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}

.custom-input-width {
  width: 5rem;
}
</style>
