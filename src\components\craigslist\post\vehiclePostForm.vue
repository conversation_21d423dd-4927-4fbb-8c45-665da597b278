<template>
  <b-card>
    <div class="media align-items-center py-3 mb-3">
      <img :src="vehicle.presentationPhoto" class="d-block ui-w-80 ui-bordered">
      <div class="media-body ml-4">
        <h4 class="font-weight-bold mb-2">{{vehicle.year}} {{vehicle.make}} {{vehicle.model}}</h4>
        <h5 class="font-weight-bold mb-2">{{vehicle.trim}} <span class="text-muted"> Stock# {{vehicle.stockNumber}}</span></h5>
      </div>
    </div>

    <div>
      <detail-row fixedPayloadWidth>
        <span slot="title">Price:</span>
        <price-input slot="payload" active v-model="dataPrice" placeholder=""></price-input>
      </detail-row>

      <auto-detail-row title='Posting title *' v-model='dataPostingTitle' />
      <auto-detail-row title='Specific Location' v-model='dataSpecificLocation' validationRule="max:55" />
      <b-row class='mt-1 pr-2'>
          <div class='label-custom-style pl-1 pt-2'>
          <span class='text-muted ml-2' >Posting Area:</span>
          </div>
          <div class='select-custom-style'>
            <multiselect
            :options='areaOptions'
            v-model='selectedArea'
            :multiple='false'
            label='text'
            @input="onAreaUpdated"
            :allowEmpty='false'
            :preselect-first="true"
            />
          </div>
      </b-row>
      <b-row class='mt-1 pr-2'>
          <div class='label-custom-style pl-1 pt-2'>
          <span class='text-muted ml-2' >Sub Area:</span>
          </div>
          <div class='select-custom-style'>
            <multiselect
            :options='subAreaOptions'
            v-model='selectedSubarea'
            :multiple='false'
            label='text'
            :disabled="!isSubAreaAllowed"
            @input='onSubareaUpdated'
            :allowEmpty='false'
            :preselect-first="true"
            />
          </div>
      </b-row>
      <b-row class='mt-1 pr-2'>
          <div class='label-custom-style pl-1 pt-2'>
          <span class='text-muted ml-2' >Posting Category:</span>
          </div>
          <div class='select-custom-style'>
            <multiselect
            :options='categoryTypeOptions'
            v-model='selectedCategoryType'
            :multiple='false'
            :allowEmpty='false'
            label='text'
            @input='categoryTypeUpdated'
            />
          </div>
      </b-row>
      <b-row class='mt-1 pr-2'>
          <div class='label-custom-style pl-1 pt-2'>
          <span class='text-muted ml-2' >Schedule posting:</span>
          </div>
          <div class='select-custom-style'>
            <multiselect
            :options='postingTypeOptions'
            v-model='selectedPostingType'
            :multiple='false'
            :allowEmpty='false'
            label='text'
            @input='onPostingTypeUpdated'
            />
          </div>
      </b-row>

      <detail-row fixedPayloadWidth>
        <span slot="title">Posting Date:</span>
        <div slot="payload" class="d-flex" style="flex-grow: 1">
          <b-input-group class="flex-nowrap flex-inline-sized status-dp">
            <b-input-group-prepend is-text>
              <i class="ion ion-md-calendar" slot="prepend"></i>
            </b-input-group-prepend>
            <datepicker :value="dataPostingDate" @input="onPostingDateUpdated" :disabled="!isSchedulePosting" format="MM/dd/yyyy" input-class="form-control"></datepicker>
          </b-input-group>
          <span class="px-2"></span>
          <b-form-select v-model="dataPostingTime" :disabled="!isSchedulePosting" :options="postingTimeOptions"></b-form-select>
        </div>
      </detail-row>
    </div>
  </b-card>
</template>

<script>
import {mapGetters} from 'vuex'
import { createHelpers } from 'vuex-map-fields'
import autoDetailRow from './../../../components/details/helpers/autoDetailRow'
import detailRow from './../../../components/details/helpers/detailRow'
import priceInput from './../../../components/_shared/priceInput'
import craigslistConstants from './../../../shared/craigslist/constants'
import commonConstants from './../../../shared/common/constants'
import datepicker from 'vuejs-datepicker'
import moment from 'moment'
import Multiselect from 'vue-multiselect'

// The getter and mutation types we're providing
// here, must be the same as the function names we've
// used in the store.
const { mapFields } = createHelpers({
  getterType: 'craigslistPost/getCraigslistPostData',
  mutationType: 'craigslistPost/updateCraigslistPostData'
})

export default {
  name: 'vehicle-post-form',
  props: {
    typePost: { type: Number, required: true }
  },
  components: {
    'auto-detail-row': autoDetailRow,
    'detail-row': detailRow,
    'datepicker': datepicker,
    'price-input': priceInput,
    'multiselect': Multiselect
  },
  data () {
    return {
      isSubAreaAllowed: false,
      subAreaOptions: [],
      areaOptions: [],
      isSchedulePosting: false,
      categoryTypeOptions: [],
      postingTypeOptions: [],
      postingTimeOptions: [],
      selectedArea: null,
      selectedSubarea: {
        value: '',
        text: 'Nothing Selected'
      },
      selectedCategoryType: null,
      selectedPostingType: null
    }
  },
  mounted () {
    this.prepareData()
  },
  computed: {
    ...mapGetters('craigslistPost', ['vehicle', 'craigslistAreas']),
    ...mapFields({
      dataPrice: 'price',
      dataPostingTitle: 'postingTitle',
      dataSpecificLocation: 'specificLocation',
      dataAreaCode: 'areaCode',
      dataSubAreaCode: 'subAreaCode',
      dataCategoryType: 'categoryType',
      dataPostingType: 'postingType',
      dataPostingDate: 'postingDate',
      dataPostingTime: 'postingTime'
    })
  },
  methods: {
    getAreaOptions () {
      return this.craigslistAreas.map(x => {
        return {
          value: x.area,
          text: x.areaDescription
        }
      })
    },
    getSubAreaOptions (areaCode) {
      const area = this.craigslistAreas.find(x => x.area === areaCode)

      if (area && area.subAreas && area.subAreas.length > 0) {
        return area.subAreas.map(x => {
          return {
            value: x.key,
            text: x.value
          }
        })
      }

      return [{
        value: '',
        text: 'Nothing Selected'
      }]
    },
    getCategoryTypeOptions () {
      return Object.keys(craigslistConstants.craigslistCategoryTypes).map(key => {
        const prop = craigslistConstants.craigslistCategoryTypes[key]

        return {
          value: prop.value,
          text: prop.description
        }
      })
    },
    getPostingTimeOptions () {
      return commonConstants.hoursOptions.map(x => {
        return {
          value: x,
          text: x
        }
      })
    },
    getPostingTypeOptions () {
      return Object.keys(craigslistConstants.craigslistPostingTypes).map(key => {
        const prop = craigslistConstants.craigslistPostingTypes[key]

        return {
          value: prop.value,
          text: prop.description
        }
      })
    },
    onAreaUpdated (newAreaCode) {
      this.dataAreaCode = newAreaCode.value
      this.subAreaOptions = this.getSubAreaOptions(newAreaCode.value)
      this.selectedSubarea = this.subAreaOptions[0]
      this.dataSubAreaCode = this.subAreaOptions[0].value
      this.isSubAreaAllowed = this.dataSubAreaCode && this.dataSubAreaCode.length > 0
    },
    onSubareaUpdated (newSubareaCode) {
      if (newSubareaCode) {
        this.dataSubAreaCode = newSubareaCode.value
      } else {
        this.dataSubAreaCode = ''
        this.selectedSubarea = {
          value: '',
          text: 'Nothing Selected'
        }
      }
    },
    categoryTypeUpdated (newcategoryType) {
      this.dataCategoryType = newcategoryType.value
    },
    onPostingTypeUpdated (newPostingType) {
      this.dataPostingType = newPostingType.value
      this.isSchedulePosting = newPostingType.value === craigslistConstants.craigslistPostingTypes.schedulePost.value
    },
    onPostingDateUpdated (newDate) {
      this.dataPostingDate = moment(newDate).format('MM/DD/YYYY')
    },
    prepareData () {
      this.areaOptions = this.getAreaOptions()
      this.isSchedulePosting = this.dataPostingType === craigslistConstants.craigslistPostingTypes.schedulePost.value
      this.categoryTypeOptions = this.getCategoryTypeOptions()
      this.postingTypeOptions = this.getPostingTypeOptions()
      this.postingTimeOptions = this.getPostingTimeOptions()
      this.selectedCategoryType = this.categoryTypeOptions[this.dataCategoryType]

      if (this.typePost === 1 || this.typePost === 3) {
        this.selectedPostingType = this.postingTypeOptions[1]
        this.selectedArea = this.areaOptions.find(x => x.value === this.dataAreaCode)
        this.subAreaOptions = this.getSubAreaOptions(this.dataAreaCode)
        if (this.dataSubAreaCode) {
          this.selectedSubarea = this.subAreaOptions.find(x => x.value === this.dataSubAreaCode)
        }
      } else {
        this.selectedPostingType = this.postingTypeOptions[0]
        this.selectedArea = this.areaOptions[0]
        this.subAreaOptions = this.getSubAreaOptions(this.selectedArea.value)
        if (this.subAreaOptions.length > 0) {
          this.selectedSubarea = this.subAreaOptions[0]
        }
      }
      this.isSubAreaAllowed = this.selectedSubarea.value && this.selectedSubarea.value.length > 0
    }
  }
}
</script>

<style scoped>
.flex-inline-sized {
  flex: 1
}
.status-dp {
  min-width: 60%;
  -ms-flex: 60;
}
.label-custom-style {
  width:226px;
}
.select-custom-style {
  width: 300px;
}
@media (max-width: 576px) {
  .select-custom-style {
    margin-left: 10px;
    width: 100%;
  }
}
@media (max-width: 595px) {
  .select-custom-style {
    margin-left: 10px;
  }
}
</style>
