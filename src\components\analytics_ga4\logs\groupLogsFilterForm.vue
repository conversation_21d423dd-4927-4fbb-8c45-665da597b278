<template>
  <b-form v-on:submit.prevent="applyFilter">
    <div class="form-row">
      <b-col :xl="6">
        <b-input-group>
          <b-form-input ref="search" v-model="filter.search" placeholder="Search..."></b-form-input>
          <b-input-group-append>
            <b-btn type="submit" variant="primary">Go</b-btn>
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </div>
  </b-form>
</template>

<script>
import baseLogsFilterForm from './baseLogsFilterForm'

export default {
  mixins: [baseLogsFilterForm],
  name: 'group-logs-filter-form'
}
</script>
