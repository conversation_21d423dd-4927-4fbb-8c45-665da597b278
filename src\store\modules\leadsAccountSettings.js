import axios from 'axios'

export default {
  namespaced: true,
  state: {
    adminAccountSettings: null,
    dealerAccountSettings: null,
    turnstileSettings: null
  },
  getters: {
    dealerAccountSettings: state => state.dealerAccountSettings,
    adminAccountSettings: state => state.adminAccountSettings,
    turnstileSettings: state => state.turnstileSettings
  },
  mutations: {
    setAdminAccountSettingsData (state, data) {
      state.adminAccountSettings = data
    },
    setDealerAccountSettingsData (state, data) {
      state.dealerAccountSettings = data
    },
    setTurnstileSettingsData (state, data) {
      state.turnstileSettings = data
    }
  },
  actions: {
    async populateAdminAccountSettingsData ({ commit }, accountId) {
      const result = await axios.get(`/api/leads/admin/accounts/${accountId}/settings`)

      commit('setAdminAccountSettingsData', result.data)

      return result.data
    },
    async updateAdminAccountSettings ({ commit }, parameters) {
      const result = await axios.post(`/api/leads/admin/accounts/${parameters.accountId}/settings`, parameters.data)

      return result.data
    },
    async populateDealerAccountSettingsData ({ commit }, accountId) {
      const result = await axios.get(`/api/leads/dealer/accounts/${accountId}/settings`)

      commit('setDealerAccountSettingsData', result.data)

      return result.data
    },
    async updateDealerAccountSettings ({ commit }, parameters) {
      const result = await axios.post(`/api/leads/dealer/accounts/${parameters.accountId}/settings`, parameters.data)

      return result.data
    },
    async populateTurnstileSettingsData ({ commit }, accountId) {
      const result = await axios.get(`/api/leads/accounts/${accountId}/turnstile-settings`)

      commit('setTurnstileSettingsData', result.data)

      return result.data
    },
    async updateTurnstileSettings ({ commit }, parameters) {
      const result = await axios.post(`/api/leads/accounts/${parameters.accountId}/turnstile-settings`, parameters.data)

      return result.data
    }
  }
}
