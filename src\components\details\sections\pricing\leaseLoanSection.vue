<template>
  <details-section title="Payment" @cancel="onCancel" v-model="mode" @save="saveHappened">
    <div class="view" v-if="mode === 'view'">

      <detail-row>
        <span slot="title">Payment Type:</span>
        <span slot="payload" class="text-capitalize">{{paymentType}}</span>
      </detail-row>

      <detail-row>
        <span slot="title">Payment Amount:</span>
        <span slot="payload">{{$locale.formatCurrency(getPaymentAmount)}}</span>
      </detail-row>

      <detail-row v-if="!isLease">
        <span slot="title">Payment Per $1,000:</span>
        <span slot="payload">{{$locale.formatCurrency(getBorrowed)}}</span>
      </detail-row>

      <detail-row>
        <span slot="title">Display Payment:</span>
        <span slot="payload">{{getDisplayPayment}}</span>
      </detail-row>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <detail-row editMode fixedPayloadWidth v-if="hasLeasePayment">
        <span slot="title">Payment Type:</span>
        <b-form-select slot="payload" v-model="paymentType" :value="paymentType" @input="onChangePaymentType" :options="getPaymentOptions"></b-form-select>
      </detail-row>

      <detail-row v-else>
        <span slot="title">Payment Type:</span>
        <span slot="payload" class="text-capitalize">{{paymentType}}</span>
      </detail-row>

      <detail-row>
        <span slot="title">Payment Amount:</span>
        <span slot="payload">{{$locale.formatCurrency(getPaymentAmount)}}</span>
      </detail-row>

      <detail-row v-if="!isLease">
        <span slot="title">Payment Per $1,000:</span>
        <span slot="payload">{{$locale.formatCurrency(getBorrowed)}}</span>
      </detail-row>

      <detail-row>
        <span slot="title">Display Payment:</span>
        <b-form-checkbox v-model="hasToDisplay" slot="payload">Display Payment</b-form-checkbox>
      </detail-row>

      <detail-row class="d-none d-sm-flex">
        <span slot="title"></span>
        <b-btn slot="payload" @click="showModal()" size="sm">Payment Calculator</b-btn>
      </detail-row>

      <b-btn class="d-sm-none w-100 mt-2" slot="payload" @click="showModal()" size="lg">Payment Calculator</b-btn>

      <b-modal
        v-model="isModalShown"
        body-class="pr-sm-0 payment-modal-calculator"
        size="lg"
        :title="getModalTitle"
        ok-title="Save Payment"
        cancel-title="Close"
        @ok="handleOk"
        footer-class="payment-modal-calculator__footer">

        <detail-row fixedPayloadWidth editMode>
          <span slot="title">Sales Price:</span>
          <span slot="payload" class="modal-info pt-sm-0">{{$locale.formatCurrency(modalData.salesPrice)}}</span>
        </detail-row>

        <detail-row fixedPayloadWidth editMode>
          <span slot="title">Sales Tax Percent:</span>
          <b-form-input slot="payload" v-model="modalData.salesTaxPercent" type="number"></b-form-input>
        </detail-row>

        <detail-row fixedPayloadWidth editMode>
          <span slot="title">Term in month:</span>
          <b-form-input slot="payload" v-model="modalData.term" type="number"></b-form-input>
        </detail-row>

        <detail-row fixedPayloadWidth editMode>
          <span slot="title">Interest Rate:</span>
          <b-form-input slot="payload" v-model="modalData.interestRatePercent" type="number"></b-form-input>
        </detail-row>

        <detail-row fixedPayloadWidth editMode>
          <span slot="title">Down Payment:</span>
          <price-input slot="payload" v-model="modalData.downPayment" placeholder="Enter Price" active></price-input>
        </detail-row>

        <detail-row mobileWrap v-if="!isLease" editMode>
          <span slot="title">$ per $1,000 Borrowed:</span>
          <span slot="payload" class="modal-info pt-2 pt-sm-0">${{loanPer1000Borrowed}}</span>
        </detail-row>

        <detail-row fixedPayloadWidth editMode v-if="isLease">
          <span slot="title">Residual Value:</span>
          <price-input slot="payload" v-model="modalData.residualValue" placeholder="Enter Price" active></price-input>
        </detail-row>

        <detail-row mobileWrap editMode>
          <span slot="title">Payment Amount:</span>
          <span slot="payload" class="modal-info pt-2 pt-sm-0">${{paymentAmount}}</span>
        </detail-row>

      </b-modal>

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import creditService from '@/services/details/CreditService'
import detailRow from '@/components/details/helpers/detailRow'
import priceInput from '@/components/_shared/priceInput'

let timerId = 0

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      mode: 'view',
      isModalShown: false,
      modalData: null,
      paymentAmount: 0,
      paymentType: 'loan',
      loanPer1000Borrowed: 0
    }
  },
  mounted () {
    this.init()
  },
  computed: {
    ...mapGetters('details', ['vehicle']),
    getModalTitle () {
      return this.isLease ? 'Lease Payment' : 'Loan Payment'
    },
    hasToDisplay: {
      get () {
        return (this.getCurrentPayment || {}).hasToDisplay
      },
      set (value) {
        this.initializeVehicleProperties()
        this.getCurrentPayment.hasToDisplay = value
      }
    },
    getCurrentPayment () {
      if (this.isLease) {
        return (this.vehicle.payment || {}).lease
      } else {
        return (this.vehicle.payment || {}).loan
      }
    },
    getPaymentAmount () {
      return (this.getCurrentPayment || {}).paymentAmount || 0
    },
    getDisplayPayment () {
      return (this.getCurrentPayment || {}).hasToDisplay ? 'Yes' : 'No'
    },
    hasLeasePayment () {
      return !!this.vehicle.isNew
    },
    isLease () {
      return this.paymentType === 'lease'
    },
    getBorrowed () {
      return ((this.vehicle.payment || {}).loan || {}).loanPer1000Borrowed || 0
    },
    getPaymentOptions () {
      return [{
        value: 'loan',
        text: 'Loan'
      }, {
        value: 'lease',
        text: 'Lease'
      }]
    }
  },
  methods: {
    init () {
      this.paymentType = (this.vehicle.isNew && (this.vehicle.payment || {}).lease) ? 'lease' : 'loan'

      let payment = this.getCurrentPayment || {}
      this.modalData = {
        salesPrice: +(this.vehicle.pricing.lowPrice || this.vehicle.pricing.highPrice),
        salesTaxPercent: payment.salesTax || 0,
        term: payment.term || 0,
        interestRatePercent: payment.interestRate || 0,
        downPayment: payment.downPayment || 0,
        residualValue: payment.residualValue || 0
      }
      this.paymentAmount = payment.paymentAmount || 0
      this.loanPer1000Borrowed = payment.loanPer1000Borrowed || 0
    },
    initializeVehicleProperties () {
      if (this.mode.toLowerCase() === 'edit') {
        if (!this.vehicle.payment) {
          this.vehicle.payment = {}
        }
        if (this.hasLeasePayment) {
          if (!this.vehicle.payment.lease) {
            this.fillPaymentLeaseDefault()
          }
        } else if (!this.vehicle.payment.loan) {
          this.fillPaymentLoanDefault()
        }
      }
    },
    showModal () {
      this.isModalShown = true
      this.calculate()
    },
    saveHappened () {
      if (!this.vehicle.payment) {
        this.vehicle.payment = {}
      }
      if (this.isLease) {
        this.vehicle.payment.loan = null
      } else {
        this.vehicle.payment.lease = null
      }
    },
    getBooleanDescription (boolValue) {
      return boolValue ? 'Yes' : 'No'
    },
    calculate () {
      if (!this.isModalShown || this.modalData.term === 0) {
        return
      }

      clearTimeout(timerId)

      timerId = setTimeout(() => {
        let data = {}

        for (let k in this.modalData) {
          data[k] = +this.modalData[k]
        }

        const paymentFunc = this.isLease ? creditService.getEstimatedLeaseAsync : creditService.getEstimatedLoanAsync

        paymentFunc.call(creditService, data).then(x => {
          this.paymentAmount = x.data.monthlyPayment
          if (!this.isLease) {
            this.loanPer1000Borrowed = x.data.loanPer1000Borrowed
          }
        })
      }, 500)
    },
    onChangePaymentType (newPaymentType) {
      this.paymentType = newPaymentType
      if (!this.vehicle.payment) {
        this.vehicle.payment = {}
      }
      if (this.isLease && !this.vehicle.payment.lease) {
        this.fillPaymentLeaseDefault()
      } else if (!this.vehicle.payment.loan) {
        this.fillPaymentLoanDefault()
      }
    },
    handleOk () {
      this.initializeVehicleProperties()
      if (this.isLease) {
        this.handleLeaseOk()
      } else {
        this.handleLoanOk()
      }
    },
    handleLeaseOk () {
      this.vehicle.payment.lease.salesTax = +this.modalData.salesTaxPercent
      this.vehicle.payment.lease.term = +this.modalData.term
      this.vehicle.payment.lease.interestRate = +this.modalData.interestRatePercent
      this.vehicle.payment.lease.downPayment = +this.modalData.downPayment
      this.vehicle.payment.lease.residualValue = +this.modalData.residualValue
      this.vehicle.payment.lease.paymentAmount = +this.paymentAmount
      this.vehicle.payment.loan = null
    },
    handleLoanOk () {
      this.vehicle.payment.loan.salesTax = +this.modalData.salesTaxPercent
      this.vehicle.payment.loan.term = +this.modalData.term
      this.vehicle.payment.loan.interestRate = +this.modalData.interestRatePercent
      this.vehicle.payment.loan.downPayment = +this.modalData.downPayment
      this.vehicle.payment.loan.paymentAmount = +this.paymentAmount
      this.vehicle.payment.loan.loanPer1000Borrowed = +this.loanPer1000Borrowed
      this.vehicle.payment.lease = null
    },
    fillPaymentLeaseDefault () {
      this.$set(this.vehicle.payment, 'lease', {
        salesTax: 0,
        term: 0,
        interestRate: 0,
        downPayment: 0,
        residualValue: 0,
        paymentAmount: 0,
        hasToDisplay: false
      })
    },
    fillPaymentLoanDefault () {
      this.$set(this.vehicle.payment, 'loan', {
        salesTax: 0,
        term: 0,
        interestRate: 0,
        downPayment: 0,
        paymentAmount: 0,
        loanPer1000Borrowed: 0,
        hasToDisplay: false
      })
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow,
    'price-input': priceInput
  },
  watch: {
    modalData: {
      handler: function () {
        this.modalData.term = +this.modalData.term
        this.calculate()
      },
      deep: true
    },
    vehicle () {
      this.init()
    }
  }
}
</script>

<style scoped lang="scss">
  .modal-info {
    padding-left: 0.9rem;
  }
</style>

<style>
  @media (max-width: 576px) {
    .payment-modal-calculator__footer > button {
      flex: 1 1 0;
    }
  }
</style>
