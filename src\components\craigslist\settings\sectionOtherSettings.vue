<template>
<editSettingsHelper title="Other Settings" @save="saveSettings" @cancel="cancel" @changeMode="changeMode" :isDisabled="isDisabled" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode">
  <div slot="settings-content">
    <detail-row :fixed-payload-width="true">
      <span slot="title">Favorite Areas:</span>
      <div v-if="isViewMode" slot="payload" class="w-100">
        <multiselect v-if="favoriteAreasSelected"
            select-label=""
            :options='favoriteAreasSelected'
            placeholder="Selected Favorite Areas"
            :multiple="false"
            :custom-label="customLabel"
            track-by="id"
          />
          <span v-else>Don't select any Favorite Areas</span>
      </div>
      <multiselect v-else
        slot="payload"
        v-model='favoriteAreasSelected'
        :options='areasOptions'
        :multiple="true"
        :close-on-select="false"
        :clear-on-select="false"
        :preserve-search="true"
        :custom-label="customLabel"
        track-by="id"
        placeholder="Select Favorite Areas"
      />
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Send Title Status:</span>
      <span v-if="isViewMode" slot="payload" size="sm">{{getSentTitleStatusDescription()}}</span>
      <multiselect v-else slot="payload" size="sm" :allowEmpty='false' v-model='hasToSendTitleStatusSelected' :options='hasToSendTitleStatusOptions' label='text' :multiple="false" :preselect-first="true"></multiselect>
    </detail-row>
    <b-form-checkbox class="flex-inline-sized status-incoming-inv my-2" v-model="data.hasToHideVin" :disabled="isViewMode">
      Send VIN#
    </b-form-checkbox>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Budget:</span>
      <span v-if="isViewMode" slot="payload">{{data.budget}}</span>
      <b-form-input v-else type='number' size="sm" slot="payload" v-model='data.budget' :disabled="isViewMode"></b-form-input>
    </detail-row>
    <b-form-checkbox class="flex-inline-sized status-incoming-inv my-2" v-model="data.hasToRemoveSoldItemsFromCraigslist" :disabled="isViewMode">
      Automatically Delete Posts For Sold Vehicles
    </b-form-checkbox>
  </div>
</editSettingsHelper>
</template>

<script>
import { mapGetters } from 'vuex'
import Multiselect from 'vue-multiselect'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '../../../globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'

export default {
  name: 'section-sold-vehicles',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    },
    isUpdatingProcessing: {
      type: Boolean,
      required: true
    }
  },
  created () {
    this.populateData()
  },
  components: {
    'multiselect': Multiselect,
    'detail-row': detailRow,
    editSettingsHelper
  },
  data () {
    return {
      isViewMode: true,
      hasToSendTitleStatusSelected: null,
      favoriteAreasSelected: [],
      hasToSendTitleStatusOptions: [
        {
          value: false,
          text: 'Don\'t Send Title Status'
        },
        {
          value: true,
          text: 'Send Title Status'
        }
      ],
      areasOptions: [],
      data: {
        hasToRemoveSoldItemsFromCraigslist: false,
        craigslistFavoriteAreaIds: [],
        hasToSendTitleStatus: false,
        hasToHideVin: false,
        budget: 0
      }
    }
  },
  computed: {
    ...mapGetters('craigslistSettings', ['settingsPutData', 'areas'])
  },
  methods: {
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    saveSettings () {
      this.isViewMode = true
      this.data.hasToSendTitleStatus = this.hasToSendTitleStatusSelected.value
      let areaIds = []
      this.favoriteAreasSelected.map(x => {
        areaIds.push(x.id)
      })
      this.data.craigslistFavoriteAreaIds = areaIds
      this.$store.commit('craigslistSettings/setOtherSettings', this.data)
      this.putSettingsData()
    },
    cancel () {
      this.isViewMode = true
      this.populateData()
    },
    putSettingsData () {
      this.$emit('putSettingsData', this.settingsPutData)
    },
    populateData () {
      this.data.hasToRemoveSoldItemsFromCraigslist = globals().getClonedValue(this.settingsPutData.hasToRemoveSoldItemsFromCraigslist)
      this.data.hasToSendTitleStatus = globals().getClonedValue(this.settingsPutData.hasToSendTitleStatus)
      this.data.hasToHideVin = !globals().getClonedValue(this.settingsPutData.hasToHideVin)
      this.data.budget = globals().getClonedValue(this.settingsPutData.budget)
      if (this.settingsPutData.hasToSendTitleStatus) {
        this.hasToSendTitleStatusSelected = this.hasToSendTitleStatusOptions[1]
      } else {
        this.hasToSendTitleStatusSelected = this.hasToSendTitleStatusOptions[0]
      }
      this.InitAreasOptions()
      this.InitFavoriteAreas()
    },
    InitAreasOptions () {
      this.areasOptions = this.areas
    },
    InitFavoriteAreas () {
      this.favoriteAreasSelected = []
      this.areas.map(x => {
        if (x.isFavoriteArea) {
          this.favoriteAreasSelected.push(x)
        }
      })
    },
    getSentTitleStatusDescription () {
      return this.hasToSendTitleStatusOptions.find(x => x.value === this.data.hasToSendTitleStatus).text
    },
    customLabel ({areaDescription, subareaDescription}) {
      if (subareaDescription) {
        return `${areaDescription}, ${subareaDescription}`
      } else {
        return `${areaDescription}`
      }
    }
  }
}
</script>

<style scoped>
 @media(min-width: 1600px) {
    .custom-settings-craigslist {
      width: 25%;
    }
 }

 @media(max-width: 1600px) {
    .custom-settings-craigslist {
      width: 30%;
    }
 }

 @media(max-width: 1200px) {
    .custom-settings-craigslist {
      width: 40%;
    }
 }

 @media(max-width: 800px) {
    .custom-settings-craigslist {
      width: 50%;
    }
 }
 @media(max-width: 400px) {
    .custom-settings-craigslist {
      width: 100%;
    }
 }
</style>
