<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Sub Model / Trim" :text="vehicle.trim"/>

    <auto-detail-row :title="snowmobileType.name" :text="getSelectedOptionOrSelf(snowmobileType).key"/>

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="Year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     name="year"
                     :options="getYearsOptions"
                     @change="onInputYear"
      />
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make, inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"
      />
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Model" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row fixedPayloadWidth editMode :error="errors[0]">
      <span slot="title">Model:</span>
      <b-form-input class="form-control" slot="payload" v-model="vehicle.model" name="model"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Gallery Display Name" rules="max:50|xml" v-slot="{errors}">
    <detail-row fixedPayloadWidth editMode :error="errors[0]">
      <span slot="title">Sub Model / Trim:</span>
      <b-form-input
        slot="payload"
        v-model="vehicle.trim"
        name="gallery display name"
      />
    </detail-row>
    </ValidationProvider>

    <auto-detail-row :title="snowmobileType.name" v-model="snowmobileType.value" :options="getNameValueOptions(snowmobileType.nameValueOptions)" enableCustom />
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import featuresHelper from '../../../../shared/details/featuresHelper'
import autoDetailRow from '../../helpers/autoDetailRow'

export default {
  name: 'vehicle-overview-snow',
  props: {
    mode: String
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'snowDetails']),
    ...mapGetters('categoryData', ['makes', 'years']),
    getYearsOptions () {
      return this.years.map(x => ({value: x, text: x}))
    },
    getMakesOptions () {
      return this.makes.map(x => ({value: x, text: x}))
    },
    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    },
    snowmobileType () {
      return this.getFeatureById(-9000)
    }
  },
  methods: {
    onInputYear (newVal) {
      this.vehicle.year = newVal.isInputMode ? newVal.text : newVal.value
    },
    onInputMakes (newVal) {
      this.vehicle.make = newVal.isInputMode ? newVal.text : newVal.value
    },
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.snowDetails, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedOptionOrSelf (attribute) {
      return featuresHelper.getSelectedOptionOrSelf(attribute)
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>
