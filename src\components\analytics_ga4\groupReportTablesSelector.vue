<template>
  <div class="row mt-3">
    <div class="col">
      <b-card>
        <b-form-group>
          <b-form-radio-group
            v-model="selectedValue"
            :options="options"
          ></b-form-radio-group>
        </b-form-group>
      </b-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'group-report-tables-selector',
  props: {
    isSegmentedByAccountSelected: { type: Boolean, required: true },
    defaultSegmentName: { type: String, required: true }
  },
  data () {
    return {
      selected: false
    }
  },
  computed: {
    options () {
      return [
        { text: 'Segment Report by ' + this.defaultSegmentName, value: false },
        { text: 'Segment Report by Account', value: true }
      ]
    },
    selectedValue: {
      get () {
        return this.isSegmentedByAccountSelected
      },
      set (newVal) {
        // avoid updates if nothing changed
        if (newVal === this.isSegmentedByAccountSelected) {
          return
        }
        this.$emit('reportTablesSelectorChanged', newVal)
      }
    }
  }
}
</script>

<style scoped>
.b-form-group {
  margin-bottom: 0;
}
</style>
