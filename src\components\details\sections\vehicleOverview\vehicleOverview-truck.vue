<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Sub Model / Trim" :text="vehicle.trim"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.truckType.name" :text="getSelectedAttributeOption(getVehicleOverviewFeatures.truckType).key"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.truckClass.name" :text="getSelectedAttributeOption(getVehicleOverviewFeatures.truckClass).key"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.exportCategory.name" :text="getSelectedAttributeOption(getVehicleOverviewFeatures.exportCategory).key"/>

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="Year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     name="year"
                     :options="getYearsOptions"
                     @change="onInputYear"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make,inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"/>
    </detail-row>
    </ValidationProvider>

    <auto-detail-row title="Model" v-model="vehicle.model" validation-rule="required|max:50|xml"/>

    <auto-detail-row title="Sub Model / Trim" v-model="vehicle.trim" validation-rule="max:100|xml"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.truckType.name" v-model="getVehicleOverviewFeatures.truckType.value" :options="getNameValueOptions(getVehicleOverviewFeatures.truckType.nameValueOptions)"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.truckClass.name" v-model="getVehicleOverviewFeatures.truckClass.value" :options="getNameValueOptions(getVehicleOverviewFeatures.truckClass.nameValueOptions)"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.exportCategory.name" v-model="getVehicleOverviewFeatures.exportCategory.value" :options="getNameValueOptions(getVehicleOverviewFeatures.exportCategory.nameValueOptions)"/>

  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import autoDetailRow from '../../helpers/autoDetailRow'
import featuresHelper from '../../../../shared/details/featuresHelper'

export default {
  name: 'vehicle-overview-truck',
  props: {
    mode: String
  },
  computed: {
    ...mapGetters('details', [
      'vehicle',
      'truckAttributes'
    ]),
    ...mapGetters('categoryData', [
      'makes',
      'years'
    ]),
    getYearsOptions () {
      return this.years.map(x => ({
        value: x,
        text: x
      }))
    },
    getMakesOptions () {
      return this.makes.map(x => ({
        value: x,
        text: x
      }))
    },
    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    },
    getVehicleOverviewFeatures () {
      return {
        truckType: this.truckAttributes.features.find(x => x.id === -10087),
        truckClass: this.truckAttributes.features.find(x => x.id === -10088),
        exportCategory: this.truckAttributes.features.find(x => x.id === -10110)
      }
    }
  },
  methods: {
    onInputYear (newVal) {
      this.vehicle.year = newVal.isInputMode ? newVal.text : newVal.value
    },
    onInputMakes (newVal) {
      this.vehicle.make = newVal.isInputMode ? newVal.text : newVal.value
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>
