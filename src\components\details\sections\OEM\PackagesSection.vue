<template>
   <details-section title="Selected OEM Packages:" @cancel="onCancel" v-model="mode" class="packages" subSection>
    <div class="view" v-if="mode === 'view'">
      <b-list-group v-if="hasSelectedOEMPackages">
        <b-list-group-item v-for="i in getVehicleOEMPackages" :key="i.packageCodeId">
          <span class="text-capitalize">{{i.name.toLowerCase()}}</span>
          <span class="float-right">${{i.price}}</span>
          <ul class="d-none d-sm-block">
            <li v-for="(i, index) in getDescription(i.description)" :key="index">{{i}}</li>
          </ul>
        </b-list-group-item>
      </b-list-group>
      <span v-else>No selected packages</span>
    </div>
    <div class="edit" v-else-if="mode === 'edit'">
      <b-list-group>
        <b-list-group-item v-for="i in getAllOEMPackages" :key="i.packageCodeId"
        :active="isActive(i.packageCodeId)"
        @click="togglePackage(i)"
        :class="{ 'bg-secondary-dark': isActive(i.packageCodeId) }">
          <span class="text-capitalize">{{i.name.toLowerCase()}}</span>
          <span class="float-right">${{i.price}}</span>
          <ul>
            <li v-for="(i, index) in getDescription(i.description)" :key="index">{{i}}</li>
          </ul>
        </b-list-group-item>
      </b-list-group>
    </div>
  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import detailsSection from '@/components/details/detailsSection'

export default {
  name: 'status-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    getVehicleOEMPackages () {
      return this.vehicle.oemPackages
    },
    hasSelectedOEMPackages () {
      return this.vehicle.oemPackages && this.vehicle.oemPackages.length > 0
    },
    getAllOEMPackages () {
      return this.metadata.vehicleOEMPackages
    }
  },
  methods: {
    isActive (id) {
      return this.vehicle.oemPackages && this.vehicle.oemPackages.some(x => x.packageCodeId === id)
    },
    togglePackage (packageItem) {
      let index = (this.vehicle.oemPackages || [])
        .map(x => x.packageCodeId)
        .indexOf(packageItem.packageCodeId)

      if (index === -1) {
        this.vehicle.oemPackages.push(packageItem)
      } else {
        this.vehicle.oemPackages.splice(index, 1)
      }
    },
    getDescription (text) {
      return text.split('- ').filter(x => x.length)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection
  }
}
</script>

<style>
.packages .edit .list-group .list-group-item:hover {
  cursor: pointer;
}

.packages .list-group .list-group-item.active {
  border: 1px solid #f1f1f2;
}
</style>
