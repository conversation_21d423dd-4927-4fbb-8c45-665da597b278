<template>
  <details-section title="Warranty" @cancel="onCancel" v-model="mode" :visible="isDisplayed" @visibilityChange='onVisibilityChange'>

    <div class="view" v-if="mode === 'view'">

      <auto-detail-row title="Warranty Type" :text="getWarrantyType"/>

      <auto-detail-row title="Buyer's Guide #1:" :text="getBuyersGuide"/>

      <auto-detail-row title="Buyer's Guide #2:" :text="getBuyersGuide2"/>

      <auto-detail-row title="Warranty Description:" :text="getWarrantyDescription"/>

    </div>
    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row title="Warranty Type" v-model="vehicle.warrantyInformation.warrantyTypeId" :options="metadata.warrantyOptions"/>

      <auto-detail-row title="Buyer's Guide #1:" v-model="vehicle.warrantyInformation.buyersGuideId" :options="accountSettings.buyersGuideSettings.items"/>

      <auto-detail-row title="Buyer's Guide #2:" v-model="vehicle.warrantyInformation.buyersGuide2Id" :options="accountSettings.buyersGuideSettings.items"/>

      <ValidationProvider immediate name="Warranty description" rules="max:50|xml" v-slot="{errors}">
      <detail-row fixedPayloadWidth editMode titlePosition="start" :error="errors[0]">
        <span slot="title">Warranty Description:</span>
        <b-form-textarea slot="payload"
                     v-model="vehicle.warrantyInformation.description"
                     placeholder="Enter warranty description"
                     :rows="3"
                     :max-rows="6"
                     name="Warranty description">
        </b-form-textarea>
      </detail-row>
      </ValidationProvider>
    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'

export default {
  name: 'warranty-section',
  data () {
    return {
      mode: 'view',
      isDisplayed: true
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.isDisplayed = val
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata', 'accountSettings']),
    getWarrantyType () {
      return this.metadata.warrantyOptions[this.vehicle.warrantyInformation.warrantyTypeId] || ''
    },
    getBuyersGuide () {
      return this.accountSettings.buyersGuideSettings.items[this.vehicle.warrantyInformation.buyersGuideId] || ''
    },
    getBuyersGuide2 () {
      return this.accountSettings.buyersGuideSettings.items[this.vehicle.warrantyInformation.buyersGuide2Id] || ''
    },
    getWarrantyDescription () {
      return this.vehicle.warrantyInformation.description
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow,
    'auto-detail-row': autoDetailRow
  }
}
</script>
