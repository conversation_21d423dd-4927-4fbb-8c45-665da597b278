<template>
  <div class="mt-5">
    <h6>Brand Export Logs</h6>
    <hr>

    <b-form class="my-4" v-on:submit.prevent="applyFilter">
      <div class="brand-export-logs-filter">
        <div class="brand-export-logs-filter-item">
          <span class="font-weight-bold">Export Name</span>
          <b-form-input
            max='200'
            v-model="filter.exportname"
            placeholder="Export Name"
            autocomplete="off"
          >
          </b-form-input>
        </div>

        <div class="brand-export-logs-filter-item">
          <span class="font-weight-bold">Report Type</span>
          <b-form-select v-model="filter.reporttype" :options="reportTypes">
          </b-form-select>
        </div>

        <div class="brand-export-logs-filter-item">
          <span class="font-weight-bold">Processing Date Range</span>
          <div class="brand-export-logs-filter-group-item">
            <b-input-group class="flex-nowrap mr-2">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="processingDateFrom"
                v-model="filter.processingdatefrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onProcessingTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.processingdatefrom"
                @click="filter.processingdatefrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="processingDateTo"
                v-model="filter.processingdateto"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onProcessingTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.processingdateto"
                @click="filter.processingdateto = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </div>
        </div>

        <div class="brand-export-logs-filter-item">
          <span class="font-weight-bold">Result Status</span>
          <b-form-select v-model="filter.resultstatus" :options="brandExportTaskStatusContextTypes">
          </b-form-select>
        </div>

        <div class="brand-export-logs-filter-item">
          <span class="font-weight-bold">Report Date Range</span>
          <div class="brand-export-logs-filter-group-item">
            <b-input-group class="flex-nowrap mr-2">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="reportDateFrom"
                v-model="filter.reportdatefrom"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date From"
                className="form-control"
                @change="onReportTimeFromInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.reportdatefrom"
                @click="filter.reportdatefrom = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
            <b-input-group class="flex-nowrap">
              <b-input-group-prepend is-text>
                <i class="ion ion-md-calendar" slot="prepend"></i>
              </b-input-group-prepend>
              <date-time-picker
                ref="reportDateTo"
                v-model="filter.reportdateto"
                :options="filterTimeOptions"
                format="MM/DD/YYYY HH:mm"
                placeholder="Date To"
                className="form-control"
                @change="onReportTimeToInputChange"
              />
              <b-input-group-append
                is-text
                v-show="filter.reportdateto"
                @click="filter.reportdateto = null"
              >
                <i class="ion ion-md-close"></i>
              </b-input-group-append>
            </b-input-group>
          </div>
        </div>
        <div class="brand-export-logs-filter-btn">
          <b-btn block variant="primary" type="submit">Submit</b-btn>
        </div>
      </div>
    </b-form>
    <div v-if="totalItem > 0">
      <paging
        class="d-none d-md-block p-0"
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="totalItem"
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
      <b-table
        :items="items"
        :fields="getTableFields"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :no-sort-reset="true"
        :no-local-sorting="true"
        striped
        outlined
        responsive
        >
        <template #cell(resultStatus)="data">
          {{getResultStatus(data.item)}}
        </template>
        <template #cell(reportDateTimeFrom)="data">
          {{getReportDateTimeFrom(data.item)}}
        </template>
        <template #cell(reportDateTimeto)="data">
          {{getReportDateTimeTo(data.item)}}
        </template>
        <template #cell(details)="data">
          <b-btn size="sm" @click="showDetails(data.item.id)">Details</b-btn>
        </template>
      </b-table>
      <paging
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="totalItem"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize"
      />
    </div>
    <div v-else class="text-muted">Not Found</div>
    <b-modal
    v-if='modalData'
    :visible='isShowModal'
    @hide='hideModal'
    size="lg">
      <template #modal-header>
        <router-link :to="{ name: 'analytics-ga4-brand-export-log-details', params: {logId: modalData.rootNode.logId} }"><h4 class="text-primary">Detail Logs</h4></router-link>
      </template>
      <b-card>
        <log-node
          :data="modalData"
          :isExpandedShallow="true"
          :isExpandedDeep="false"
        />
      </b-card>
      <template #modal-footer>
        <b-btn variant="secondary" size="md" @click="hideModal">Close</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import constants from '@/shared/analytics/constants'
import BrandExportService from '@/services/analytics/BrandExportService'
import moment from 'moment'

export default {
  name: 'analytics-brand-export-logs',
  props: {
    items: { type: Array, required: true },
    totalItem: { type: Number, required: true },
    filter: { type: Object, required: true }
  },
  data () {
    return {
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      },
      modalData: null,
      isShowModal: false
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'paging': () => import('@/components/_shared/paging'),
    'log-node': () => import('@/components/_shared/logItemNode.vue')
  },
  computed: {
    getTableFields () {
      return [
        {
          key: 'exportName',
          label: 'Export Name',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: constants.brandExportLogsSortTypes.exportNameAsc,
          sortTypeDesc: constants.brandExportLogsSortTypes.exportNameDesc
        },
        {
          key: 'brandExportReportTask.data.type',
          label: 'Report Type',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            let res = constants.reportTypes.find(x => x.value === value)
            return res ? res.text : 'Undefined'
          }
        },
        {
          key: 'endProcessingDateTime',
          label: 'Processing Date Time',
          tdClass: 'py-2 align-middle',
          formatter: value => moment(value).format('MM/DD/YYYY hh:mm:ss A'),
          sortable: true,
          sortTypeAsc: constants.brandExportLogsSortTypes.processingDateTimeAsc,
          sortTypeDesc: constants.brandExportLogsSortTypes.processingDateTimeDesc
        },
        {
          key: 'resultStatus',
          label: 'Result Status',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'reportDateTimeFrom',
          label: 'Report Date From',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: constants.brandExportLogsSortTypes.reportDateFromAsc,
          sortTypeDesc: constants.brandExportLogsSortTypes.reportDateFromDesc
        },
        {
          key: 'reportDateTimeto',
          label: 'Report Date To',
          tdClass: 'py-2 align-middle',
          sortable: true,
          sortTypeAsc: constants.brandExportLogsSortTypes.reportDateToAsc,
          sortTypeDesc: constants.brandExportLogsSortTypes.reportDateToDesc
        },
        {
          key: 'details',
          label: 'Details',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    refProcessingDateFrom () {
      return (this.$refs.processingDateFrom || {}).$el || {}
    },
    refProcessingDateTo () {
      return (this.$refs.processingDateTo || {}).$el || {}
    },
    refReportDateFrom () {
      return (this.$refs.reportDateFrom || {}).$el || {}
    },
    refReportDateTo () {
      return (this.$refs.reportDateTo || {}).$el || {}
    },
    reportTypes () {
      return constants.reportTypes
    },
    brandExportTaskStatusContextTypes () {
      return constants.brandExportTaskStatusContextTypes
    },
    tableSortBy () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.getTableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    getResultStatus (item) {
      if (item.brandExportReportCompletedTask) {
        let res = constants.exportTaskTypes.find(x => x.value === item.brandExportReportCompletedTask.data.status)
        if (res) {
          return res.text
        }
      }

      return ''
    },
    getReportDateTimeFrom (item) {
      if (item.brandExportReportTask) {
        if (item.brandExportReportTask.data.reportDateFrom) {
          return moment(item.brandExportReportTask.data.reportDateFrom).format('MM/DD/YYYY hh:mm:ss A')
        } else {
          return moment(item.brandExportReportTask.data.reportMonthFrom).format('MM/DD/YYYY hh:mm:ss A')
        }
      }

      return ''
    },
    getReportDateTimeTo (item) {
      if (item.brandExportReportTask) {
        if (item.brandExportReportTask.data.reportDateTo) {
          return moment(item.brandExportReportTask.data.reportDateTo).format('MM/DD/YYYY hh:mm:ss A')
        } else {
          return moment(item.brandExportReportTask.data.reportMonthTo).format('MM/DD/YYYY hh:mm:ss A')
        }
      }

      return ''
    },
    onProcessingTimeFromInputChange (newVal) {
      this.refProcessingDateFrom.value = newVal || this.filter.processingdatefrom || null
    },
    onProcessingTimeToInputChange (newVal) {
      this.refProcessingDateTo.value = newVal || this.filter.processingdateto || null
    },
    onReportTimeFromInputChange (newVal) {
      this.refReportDateFrom.value = newVal || this.filter.reportdatefrom || null
    },
    onReportTimeToInputChange (newVal) {
      this.refReportDateTo.value = newVal || this.filter.reportdateto || null
    },
    pageChanged (newPage) {
      this.filter.page = newPage
      this.applyFilter()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.applyFilter()
    },
    onSortChanged (value) {
      const sortingColumn = this.getTableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.applyFilter()
    },
    applyFilter () {
      this.$emit('applyFilter', this.filter)
    },
    hideModal () {
      this.isShowModal = false
    },
    showDetails (id) {
      BrandExportService.getBrandExportsLogDetails(id).then(res => {
        this.modalData = res.data.model
        this.isShowModal = true
      }).catch(ex => {
        this.$toaster.error('Something went wrong')
        this.$logger.handleError(ex, 'Cannot get brand export log detail item')
      })
    }
  }
}
</script>

<style scoped>
  .brand-export-logs-filter {
    display: flex;
    flex-direction: row;
    flex-shrink: 1;
  }
  .brand-export-logs-filter-item {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    display: flex;
    flex-direction: column;
    flex-shrink: 1;
  }
  .brand-export-logs-filter-group-item {
    display: flex;
    flex-direction: row;
    flex-shrink: 1;
  }
  .brand-export-logs-filter-btn {
    margin-top: 1.25rem;
    margin-right: 0.5rem;
  }

  @media (max-width: 1572px) {
    .brand-export-logs-filter {
      flex-direction: column;
    }
    .brand-export-logs-filter-group-item.div:nth-child(1) {
      margin-right: 0.5rem;
    }
    .brand-export-logs-filter-item {
      margin-top: 0.5rem;
    }
  }
</style>
