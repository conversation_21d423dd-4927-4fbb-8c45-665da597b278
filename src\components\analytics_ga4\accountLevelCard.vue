<template>
  <div class="row mt-3">
    <div class="col">
      <b-card>
        <div class="float-left mr-3">
          <b-button class="return-button" variant="primary" @click="onClick"><i class="ion ion-md-return-left mr-1"></i>Return to group</b-button>
        </div>
        <div class="float-left">
          Viewing Analytics for<br>
          <strong><slot></slot></strong>
        </div>
      </b-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'account-level-card',
  props: {
    accountName: String
  },
  methods: {
    onClick () {
      this.$emit('backToGroup')
    }
  }
}
</script>

<style scoped>
  .return-button {
    height: 45px;
  }
</style>
