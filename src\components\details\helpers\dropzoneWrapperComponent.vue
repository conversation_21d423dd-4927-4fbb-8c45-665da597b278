<template>
  <div class="h-100 dz-wrapper" :class="{'one-item-mode': maxFiles === 1}">
    <div class="h-100">
      <vue-dropzone
        v-model="files"
        id="my-dropzone"
        :options="dropzoneOptions"
        ref="dropzoneInstance"
        :useCustomSlot=true
        class="h-100 w-100 p-0 row dz-file-upload"
      >
        <div class="dropzone-custom-content">
          <h5 class="dropzone-custom-title">{{message}}</h5>
          <p class="subtitle">or</p>
          <div class="btn btn-primary">Click to Upload</div>
        </div>
      </vue-dropzone>
    </div>
  </div>
</template>

<style src="@/vendor/libs/vue-dropzone/vue-dropzone.scss" lang="scss"></style>

<script>
import vue2Dropzone from 'vue2-dropzone'
import cardComponent from './cardComponent'
import {convertBytesToIntMB} from '@/helpers/convert.helper'

export default {
  props: {
    message: String,
    uploadPath: String,
    maxFiles: Number,
    maxPhotoWidth: {
      type: Number,
      default: 1024
    },
    maxFileSizeInBytes: {
      type: Number,
      default: 314572800 // 300MB
    },
    acceptedFiles: String,
    multipleFileMode: Boolean,
    previewImage: String,
    autoUpload: {
      type: Boolean,
      default: false
    }
  },
  components: {
    vueDropzone: vue2Dropzone,
    'card-component': cardComponent
  },
  data () {
    const self = this

    let template = `
            <div class="card-component">
              <section class="card-component__header">
                <div class="photo-fit-container">
                  <div class="photo-wrapper">
                    ${
  self.previewImage
    ? '<img data-dz-thumbnail class="upload-image-preview" src="' + self.previewImage + '">'
    : '<img data-dz-thumbnail class="upload-image-preview" />'
}

                  </div>
                  <div class="dz-progress">
                    <span class="dz-upload" data-dz-uploadprogress></span>
                  </div>
                  <div class="dz-success-mark"></div>
                  <div class="dz-error-mark" data-dz-remove></div>
                </div>
              </section>
              <section class="card-component__body">
                <div>
                  <div data-dz-name style="overflow: hidden; white-space: nowrap;"></div>
                  <div data-dz-size></div>
                  <div class="dz-error-message-wrapper"><span data-dz-errormessage></span></div>
                </div>
              </section>
              <section class="card-component__footer" v-if="hasFooter">
                <button type="button" class="btn delete-photo-button btn-link" data-dz-remove>Remove File</button>
              </section>
            </div>`
    return {
      isErrorOccurred: false,
      filesWithError: [],
      removeServiceClasses: (file) => {
        file.previewElement.classList.remove('dz-error')
        file.previewElement.classList.remove('dz-success')
      },
      dropzoneOptions: {
        url: this.uploadPath,
        resizeWidth: this.maxPhotoWidth,
        timeout: 300000,
        resizeQuality: 1,
        maxFiles: this.maxFiles,
        acceptedFiles: this.acceptedFiles,
        parallelUploads: 1,
        maxFilesize: convertBytesToIntMB(this.maxFileSizeInBytes),
        filesizeBase: 1000,
        autoProcessQueue: this.autoUpload,
        uploadMultiple: false,
        init: function () {
          this.on('addedfile', function (file) {
            if (!file.uuid) {
              file.uuid = self.$uuid.v4()

              file.retryFunc = function () {
                file.status = 'queued'
                file.previewElement.classList.remove('dz-error')

                let index = self.filesWithError.findIndex(e => e.uuid === file.uuid)

                if (index !== -1) {
                  self.filesWithError.splice(index, 1)
                }

                if (self.filesWithError.length === 0) {
                  self.isErrorOccurred = false
                }
              }

              self.$emit('addedFile')
            } else {
              file.previewElement.classList.remove('dz-error')
            }

            let button = document.createElement('button')
            button.addEventListener('click', () => {
              file.retryFunc()
              self.$refs.dropzoneInstance.processQueue()
            })
            button.innerText = 'Try Again'

            button.classList.add('btn')
            button.classList.add('btn-secondary')
            button.classList.add('retry-button')
            button.classList.add('btn-sm')
            button.classList.add('w-100')

            file.previewElement.appendChild(button)
          })
        },
        success: function (file) {
          self.removeServiceClasses(file)
          return file.previewElement.classList.add('dz-success')
        },
        error: function (file, error) {
          const maxFileSize = (self.dropzoneOptions.filesizeBase || 1000) *
            (self.dropzoneOptions.filesizeBase || 1000) *
            self.dropzoneOptions.maxFilesize

          self.$toaster.error(error)

          if (file.size > maxFileSize) {
            self.$refs.dropzoneInstance.removeFile(file)
          } else {
            self.removeServiceClasses(file)
            file.previewElement.classList.add('dz-error')
          }
        },
        sending (file, xhr, formData) {
          file.xhr.ontimeout = () => {
            file.status = 'error'
            this.emit('error', file, 'Upload timeout', null)
          }
          self.$emit('uploadingStarted')
          file.xhr.setRequestHeader('UniqueId', file.uuid)
        },
        complete: (q) => {
          if (q.status === 'error') {
            this.filesWithError.push(q)
            this.isErrorOccurred = true
          }
        },
        maxfilesexceeded: function (file) {
          self.$refs.dropzoneInstance.removeFile(file)
          let index = self.filesWithError.findIndex(e => e.uuid === file.uuid)

          if (index !== -1) {
            self.filesWithError.splice(index, 1)
          }

          if (self.filesWithError.length === 0) {
            self.isErrorOccurred = false
          }
        },
        queuecomplete: () => {
          this.$emit('uploadComplete', !this.isErrorOccurred)
        },
        previewTemplate: !this.multipleFileMode
          ? '<div class="col-12">' + template + '</div>'
          : '<div class="col-6 col-sm-4 col-lg-3">' + template + '</div>'

      },
      files: null
    }
  },
  methods: {
    sendFiles () {
      if (this.filesWithError.length > 0) {
        this.filesWithError.forEach(x => x.retryFunc())
      }

      let files = this.$refs.dropzoneInstance.getQueuedFiles()

      if (files.length === 0) {
        this.$emit('uploadComplete', true)
      } else {
        this.$refs.dropzoneInstance.processQueue()
      }
    }
  }
}
</script>

<style>
  .dz-wrapper .dz-align {
    justify-content: center;
    align-items: center;
  }

  .dz-wrapper .dz-wrapper .dz-default.dz-message {
    padding: 0;
  }

  .dz-wrapper .upload-image-preview {
    max-width: 100%;
    object-position: center;
    object-fit: cover;
    width: 100%;
    z-index: 1;
  }

  .dz-wrapper .dropzone-custom-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1;
  }

  .dz-wrapper .dropzone-custom-title {
    margin: 0;
  }

  .dz-wrapper .card-component {
    margin: 0.3rem 0;
    width: 100%;
  }

  .dz-wrapper .card-component__body {
    border: 1px solid #eee;
    padding: 10px;
    font-size: 0.8rem;
    margin-top: -1px;
  }

  .dz-wrapper .delete-photo-button {
    padding: 0;
    color: #C90F17;
    font-size: 0.7rem;
    width: 100%;
  }

  .dz-wrapper .photo-wrapper {
    width: 100%;
  }

  .dz-wrapper .delete-photo-button:hover {
    color: #af0d14;
  }

  .dz-wrapper .subtitle {
    margin: 0;
    padding: .3rem 0 .5rem 0;
  }

  .dz-wrapper .card-component__footer {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #eee;
    padding: 4px 10px;
    margin-top: -1px;
  }

  .dz-wrapper .dz-file-upload {
    margin-left: 0;
  }

  .dz-wrapper .dz-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    width: 80%;
    height: 0.7rem;
    border-radius: 10px;
    overflow: hidden;
  }

  .dz-wrapper .card-component__header {
    border: 1px solid #eee;
  }

  .dz-processing .dz-progress {
    background: rgb(136,151,170);
  }

  .dz-wrapper .dz-error .dz-progress, .dz-wrapper .dz-success .dz-progress {
    display: none;
  }

  .dz-wrapper .dz-progress .dz-upload {
    height: 100%;
    background: #C90F17;
    display: block;
    width: 0;
  }

  .dz-wrapper .one-item-mode .card-component {
    width: 100%;
  }

  .dz-wrapper .one-item-mode .card-component__header {
    position: relative;
    padding-bottom: 56.25%;
    border: 1px solid #eee;
  }

  .dz-wrapper .one-item-mode .photo-fit-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .dz-wrapper .dz-error-mark, .dz-wrapper .dz-success-mark {
    position: absolute;
    z-index: 2;
    top: 50%;
    left: 50%;
    display: none;
    margin-top: -1.875rem;
    margin-left: -1.875rem;
    width: 3.75rem;
    height: 3.75rem;
    border-radius: 50%;
    background-position: 50%;
    background-size: 1.875rem 1.875rem;
    background-repeat: no-repeat;
    -webkit-box-shadow: 0 0 1.25rem rgba(0,0,0,.06);
    box-shadow: 0 0 1.25rem rgba(0,0,0,.06);
  }

  .dz-wrapper .dz-error-message-wrapper {
    color: #C90F17;
  }

  .dz-wrapper .default-style .dz-error-mark, .dz-wrapper .default-style .dz-success-mark {
    background-color: rgba(24,28,33,.1);
  }

  .dz-wrapper .dz-error .dz-error-mark, .dz-wrapper .dz-success .dz-success-mark {
    display: block;
  }

  .dz-wrapper .retry-button {
    display: none;
  }

  .dz-wrapper .dz-error .retry-button {
    display: block;
  }

  .dz-wrapper .dz-processing .card-component__footer {
    display: none;
  }

  .dz-wrapper .dz-error .card-component__footer {
    display: flex;
  }
</style>
