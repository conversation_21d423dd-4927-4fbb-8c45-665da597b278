<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Classified Listings (Local Listings/National Classifieds)" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row title-position="start" :large-payload-width="true">
        <span slot="title">Listing Quantity:</span>
        <div slot="payload" class="d-flex flex-row w-100">
          <span>{{settings.ListingPercentageClassified}}</span>
          <span class="ml-2 align-self-center custom-text-nowrap">% of all Listings are Local Listings/National Classifieds.</span>
        </div>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Best Offer Option:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsBestOfferEnabled" :options="getBestOfferOptions"></b-form-select>
          <span v-else>{{getBestOfferDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
           Select "Best Offer Enabled" to allow Offers on your eBay Local Listings
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Listing Title Preference:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.TitlePreferenceType" :options="getListingTitlePreferenceOptions"></b-form-select>
          <span v-else>{{getListingTitlePreferenceDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Select your preference for how eBay Auto-Sync Titles are generated.
            <br>
            Automatically-generated titles may include Make, Model, Trim, eBiz Keywords and other Key Features.
          </b-form-text>
        </b-form-group>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import globals from '@/globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import ebayOptions from '@/shared/ebay/ebayOptions'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  computed: {
    getBestOfferOptions () {
      return ebayOptions.bestOfferOptions
    },
    getListingTitlePreferenceOptions () {
      return ebayOptions.listingTitlePreferenceOptions
    },
    getBestOfferDesc () {
      return (ebayOptions.bestOfferOptions.find(x => x.value === this.settings.IsBestOfferEnabled) || {text: '-'}).text
    },
    getListingTitlePreferenceDesc () {
      return (ebayOptions.listingTitlePreferenceOptions.find(x => x.value === this.settings.TitlePreferenceType) || {text: '-'}).text
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateAutomatedClassifiedListingsSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Classified Listing Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, 'Cannot update eBay automated classified listing settings', this.settingsToUpdate)
        }
      }).finally(() => {
        this.isViewMode = true
        this.isUpdatingProcessed = false
        this.$emit('refresh')
      })
    },
    copySettings () {
      this.settingsToUpdate = globals().getClonedValue(this.settings)
    }
  },
  watch: {
    'settings': {
      deep: true,
      handler: function () {
        this.copySettings()
      }
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
.custom-input-width {
  width: 5rem;
}
</style>
