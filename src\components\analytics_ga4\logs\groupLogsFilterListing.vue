<template>
  <b-table
    :fields="getTableFields"
    :items='items'
    :bordered="false"
    :no-sort-reset="true"
    :no-local-sorting="true"
    hover
    striped
    responsive
    :sort-by="tableSortBy"
    :sort-desc="tableSortDesc"
    @sort-changed="onSortChanged">
    <template #cell(show_details)="row">
      <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
        {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
      </b-button>
      <b-btn size="sm" v-if="row.item.isDeleteAllowed" variant="primary" @click="onDelete(row.item.id)">Delete</b-btn>
    </template>
    <template #row-details="row">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Rebuild Type:</b></b-col>
          <b-col col>{{ row.item.rebuildType }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Priority:</b></b-col>
          <b-col col>{{ row.item.priority }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Task Id:</b></b-col>
          <b-col col>{{ row.item.id }}</b-col>
        </b-row>
        <b-button size="sm" @click="row.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </b-table>
</template>

<script>
import baseLogsFilterListing from './baseLogsFilterListing'
import analyticsConstants from '@/shared/analytics/constants'

export default {
  mixins: [baseLogsFilterListing],
  name: 'analytics-group-logs-listing',
  computed: {
    getTableFields () {
      return [
        {
          key: 'reportGroupId',
          sortable: false,
          label: 'Group Id'
        },
        {
          key: 'userName',
          sortable: false,
          label: 'User'
        },
        {
          key: 'dateFrom',
          sortable: false,
          label: 'From'
        },
        {
          key: 'dateTo',
          label: 'To'
        },
        {
          key: 'processStatus',
          label: 'Status'
        },
        {
          key: 'createdDateTime',
          label: 'Created At',
          sortable: true,
          sortTypeAsc: analyticsConstants.groupLogsSortTypes.createdAtAsc,
          sortTypeDesc: analyticsConstants.groupLogsSortTypes.createdAtDesc
        },
        {
          key: 'lastProcessDateTime',
          label: 'Processed At',
          sortable: true,
          sortTypeAsc: analyticsConstants.groupLogsSortTypes.processedAtAsc,
          sortTypeDesc: analyticsConstants.groupLogsSortTypes.processedAtDesc
        },
        {
          key: 'show_details',
          label: 'Manage'
        }
      ]
    }
  }
}
</script>
