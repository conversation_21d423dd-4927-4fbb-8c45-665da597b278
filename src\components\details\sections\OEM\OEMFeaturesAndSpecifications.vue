<template>
  <details-section
    mode="view"
    title="OEM Standard Features"
    :visible="isVisible"
    @visibilityChange='onVisibilityChange'
    canBeHidden
    disableEdit
    v-if="isFeaturesSpecificationsExists"
  >
    <div class="pt-2">
      <collapse-section v-for="(i) in vehicle.vehicleDataStandardFeatures" :key="i.header" :title="i.header" class="px-2 px-md-3">
        <div class="collapse-body">
          <ul class="collapse-body_list text-muted">
            <li v-for="(featureSpecificationDesc, index) in i.featureDescriptions" :key="index">
              {{featureSpecificationDesc}}
            </li>
          </ul>
        </div>
      </collapse-section>
    </div>
  </details-section>
</template>

<script>
import {mapGetters} from 'vuex'
import vehicleTypes from '../../../../shared/common/vehicle/vehicleTypes'
import detailsSection from '../../detailsSection'
import collapseSection from '../../../_shared/CollapseSection'

export default {
  name: 'OEMFeaturesAndSpecifications',
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    isVisible () {
      return this.vehicle.isFeaturesSpecificationsTurnedOn
    },
    isFeaturesSpecificationsExists () {
      return this.vehicle.vehicleType === vehicleTypes.Passenger.value &&
        this.vehicle.vehicleDataStandardFeatures &&
        this.vehicle.vehicleDataStandardFeatures.length > 0
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.vehicle.isFeaturesSpecificationsTurnedOn = val
    }
  },
  components: {
    'details-section': detailsSection,
    'collapse-section': collapseSection
  }
}
</script>

<style scoped>
  @media screen and (min-width: 992px) {
    .collapse-body_list {
      column-count: 2;
      column-gap: 3rem;
    }
  }
</style>
