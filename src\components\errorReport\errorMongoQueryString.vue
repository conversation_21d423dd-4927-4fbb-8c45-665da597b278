<template>
  <div>
    <b-form-group class="mt-4">
      <b-input-group prepend="Query" class="mb-2">
        <b-form-input v-model="mongoQueryCommand.queryString"></b-form-input>
      </b-input-group>
      <b-row>
        <b-col xs="12" xl="6">
          <b-input-group prepend="Projection" class="mb-2">
            <b-form-input v-model="mongoQueryCommand.projectionString"></b-form-input>
          </b-input-group>
        </b-col>
        <b-col xs="12" xl="6">
          <b-input-group prepend="Sort" class="mb-2">
            <b-form-input v-model="mongoQueryCommand.sortString"></b-form-input>
          </b-input-group>
        </b-col>
      </b-row>
      <hr>
      <div class="p-1">
        <query-match :matches="mongoQueryCommand.matches" :isInnerGroup="false" />
        <div class="p-2 border rounded">
          <b-row>
            <b-col xs="12" xl="6">
              <b-form-group label="projection">
                <template slot="label">
                  <strong>Projection:</strong>
                </template>
                <div v-for="(item, index) in mongoQueryCommand.projection" :key='index'>
                  <b-input-group class="mt-1">
                    <b-form-select class="mw-mte-xl-50 mw-lte-sm-40" :options="availableFieldOptions" v-model="item.field"></b-form-select>
                    <b-form-select v-model="item.operator">
                      <option
                        v-for="option in projectionAvailableOperatorOptions"
                        :key = "option.text"
                        :value="option">
                      {{ option.text }}
                      </option>
                    </b-form-select>
                    <b-btn @click="removeProjection(index)" variant="primary" :disabled="index === 0">X</b-btn>
                  </b-input-group>
                </div>
                <b-btn variant="success" @click="addProjection()" size="sm" class="mt-1">Add</b-btn>
              </b-form-group>
            </b-col>
            <b-col xs="12" xl="6">
              <b-form-group label="sort">
                <template slot="label">
                  <strong>Sort:</strong>
                </template>
                <div v-for="(item, index) in mongoQueryCommand.sort" :key='index'>
                  <b-input-group class="mt-1">
                    <b-form-select class="mw-mte-xl-50 mw-lte-sm-40" :options="availableFieldOptions" v-model="item.field"></b-form-select>
                    <b-form-select v-model="item.operator">
                      <option
                        v-for="option in sortAvailableOperatorOptions"
                        :key = "option.text"
                        :value="option">
                      {{ option.text }}
                      </option>
                    </b-form-select>
                    <b-btn @click="removeSort(index)" variant="primary" :disabled="index === 0">X</b-btn>
                  </b-input-group>
                </div>
                <b-btn variant="success" @click="addSort()" size="sm" class="mt-1">Add</b-btn>
              </b-form-group>
            </b-col>
          </b-row>
        </div>
      </div>
    </b-form-group>
  </div>
</template>

<script>
import moment from 'moment-timezone'
import { availableFields, mongoProjectionOperators, mongoSortOperators, errorMonitorTimezone } from '@/shared/errorReport/constants'
import queryMatch from '@/components/errorReport/queryMatch'

export default {
  props: {
    filters: { type: Object, required: true },
    mongoQueryCommand: { type: Object, required: true }
  },
  data () {
    return {
      mongoProjectionOperators,
      mongoSortOperators
    }
  },
  components: {
    'query-match': queryMatch
  },
  computed: {
    availableFieldOptions () {
      return availableFields
    },
    projectionAvailableOperatorOptions () {
      return mongoProjectionOperators
    },
    sortAvailableOperatorOptions () {
      return mongoSortOperators
    }
  },
  methods: {
    addProjection () {
      this.mongoQueryCommand.projection.push({
        field: availableFields[2],
        operator: mongoProjectionOperators[0]
      })
    },
    removeProjection (index) {
      this.mongoQueryCommand.projection.splice(index, 1)
    },
    addSort () {
      this.mongoQueryCommand.sort.push({
        field: availableFields[2],
        operator: mongoSortOperators[0]
      })
    },
    removeSort (index) {
      this.mongoQueryCommand.sort.splice(index, 1)
    },
    matchToString (match, isLastMatch, isInnerMatch) {
      let result = ''
      let fieldsNotEmpty = match.fields.length !== 0
      let innerMatchesNotEmpty = match.innerMatches.length !== 0
      if (fieldsNotEmpty || innerMatchesNotEmpty) {
        result += '{ ' + match.conjugation + ' : [ '
      }
      if (fieldsNotEmpty) {
        match.fields.forEach((field, fieldIndex) => {
          let isFieldLast = fieldIndex === match.fields.length - 1
          let value = field.operator.isInput ? field.operator.value.substr(0, field.operator.value.indexOf('\\')) + field.input + field.operator.value.substr(field.operator.value.indexOf('\\') + 1) : field.operator.value
          result += '{ "' + field.name + '" :' + value + '}'
          if (isFieldLast && !innerMatchesNotEmpty) {
            result += ' ] }'
          } else {
            result += ', '
          }
        })
      }
      if (innerMatchesNotEmpty) {
        match.innerMatches.forEach((innerMatch, innerMatchIndex) => {
          let isLastInnerMatch = innerMatchIndex === match.innerMatches.length - 1
          result += this.matchToString(innerMatch, innerMatchIndex === match.innerMatches - 1, true)
          if (!isLastInnerMatch) {
            if (innerMatch.innerMatches.length !== 0 || !isLastMatch) {
              result += ', '
            }
          } else {
            result += ' ] }'
          }
        })
      }
      if (!isInnerMatch && (fieldsNotEmpty || innerMatchesNotEmpty)) {
        result += ', '
      }
      return result
    },
    buildQueryString () {
      let queryString = '{ $and : [ '
      this.mongoQueryCommand.matches.forEach((match, matchIndex) => {
        queryString += this.matchToString(match, matchIndex === this.mongoQueryCommand.matches - 1, false)
      })
      let dateFrom = moment(this.filters.dateFrom, 'MM/DD/YYYY HH:mm A')
      let dateTo = moment(this.filters.dateTo, 'MM/DD/YYYY HH:mm A')
      queryString += '{ "' + availableFields[8] + '" :' + ' { $gte : ISODate("' + moment().tz(errorMonitorTimezone).set({'year': dateFrom.year(), 'month': dateFrom.month(), 'date': dateFrom.date(), 'hour': dateFrom.hour(), 'minute': dateFrom.minute(), 'seconds': 0, 'milliseconds': 0}).format() + '") } }, '
      queryString += '{ "' + availableFields[8] + '" :' + ' { $lte : ISODate("' + moment().tz(errorMonitorTimezone).set({'year': dateTo.year(), 'month': dateTo.month(), 'date': dateTo.date(), 'hour': dateTo.hour(), 'minute': dateTo.minute(), 'seconds': 0, 'milliseconds': 0}).format() + '") } }'
      let categoriesFilter = this.filters.errorreporttypes.split(',')
      if (this.filters.errorreporttypes !== '') {
        let categoriesMatchString = ', { $or : [ '
        categoriesFilter.forEach((category, index) => {
          categoriesMatchString += '{ "' + availableFields[4] + '" : "' + category.toLowerCase() + '" }'
          categoriesMatchString += categoriesFilter.length > 1 && index !== categoriesFilter.length - 1 ? ', ' : ''
        })
        queryString += categoriesMatchString + ' ] }'
      }
      queryString += ' ] }'
      return queryString
    },
    buildProjectionString () {
      let projectionString = ''
      if (this.mongoQueryCommand.projection.length > 0) {
        projectionString += '{ '
        this.mongoQueryCommand.projection.forEach((item, index) => {
          let value = item.operator.value
          projectionString += '"' + item.field + '": ' + value
          projectionString += this.mongoQueryCommand.projection.length > 1 && index !== this.mongoQueryCommand.projection.length - 1 ? ', ' : ''
        })
        projectionString += ' }'
      }
      return projectionString
    },
    buildSortString () {
      let sortString = ''
      if (this.mongoQueryCommand.sort.length > 0) {
        sortString += '{ '
        this.mongoQueryCommand.sort.forEach((item, index) => {
          let value = item.operator.value
          sortString += '"' + item.field + '": ' + value
          sortString += this.mongoQueryCommand.sort.length > 1 && index !== this.mongoQueryCommand.sort.length - 1 ? ', ' : ''
        })
        sortString += ' }'
      }
      return sortString
    }
  },
  watch: {
    'mongoQueryCommand.matches': {
      deep: true,
      immediate: true,
      handler: function () {
        this.mongoQueryCommand.queryString = this.buildQueryString()
      }
    },
    'mongoQueryCommand.dateFrom': {
      deep: true,
      immediate: true,
      handler: function () {
        this.mongoQueryCommand.queryString = this.buildQueryString()
      }
    },
    'mongoQueryCommand.dateTo': {
      deep: true,
      immediate: true,
      handler: function () {
        this.mongoQueryCommand.queryString = this.buildQueryString()
      }
    },
    'mongoQueryCommand.categories': {
      deep: true,
      immediate: true,
      handler: function () {
        this.mongoQueryCommand.queryString = this.buildQueryString()
      }
    },
    'mongoQueryCommand.projection': {
      deep: true,
      immediate: true,
      handler: function () {
        this.mongoQueryCommand.projectionString = this.buildProjectionString()
      }
    },
    'mongoQueryCommand.sort': {
      deep: true,
      immediate: true,
      handler: function () {
        this.mongoQueryCommand.sortString = this.buildSortString()
      }
    }
  }
}
</script>

<style scoped>
.custom-drop-down /deep/ .dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}
@media (min-width: 1200px) {
  .mw-mte-xl-50 {
    max-width: 50%;
  }
}
@media (max-width: 768px) {
  .mw-lte-sm-40 {
    max-width: 40%;
  }
}
</style>
