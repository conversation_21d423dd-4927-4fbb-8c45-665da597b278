<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
    @accountNameClicked="onAccountNameClicked"
  ></common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'group-website-engagement-by-account-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'account',
          label: 'Account Name',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteEngagementSortTypes.accountNameAsc,
          sortTypeDesc: analyticsConstants.websiteEngagementSortTypes.accountNameDesc
        },
        {
          key: 'pageViewsPerSession',
          label: 'Page Views per Session',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteEngagementSortTypes.pageViewsPerSessionAsc,
          sortTypeDesc: analyticsConstants.websiteEngagementSortTypes.pageViewsPerSessionDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'avgSessionDuration',
          label: 'Avg. Time On Site',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteEngagementSortTypes.avgTimeOnSiteAsc,
          sortTypeDesc: analyticsConstants.websiteEngagementSortTypes.avgTimeOnSiteDesc,
          formatter: val => this.$locale.getSecondsDurationFormatted(val)
        },
        {
          key: 'bounceRate',
          label: 'Bounce Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.websiteEngagementSortTypes.bounceRateAsc,
          sortTypeDesc: analyticsConstants.websiteEngagementSortTypes.bounceRateDesc,
          formatter: val => `${val}%`
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  },
  methods: {
    onAccountNameClicked (account) {
      this.$emit('accountNameClicked', account)
    }
  }
}
</script>

<style scoped>

</style>
