<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Sub Model / Trim" :text="vehicle.trim"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.rvClass.name" :text="getSelectedAttributeOption(getVehicleOverviewFeatures.rvClass).key"/>

    <auto-detail-row title="Engine Details" :text="vehicle.engine"/>

    <auto-detail-row title="Fuel Type" :text="getFuelType"/>

    <auto-detail-row title="Transmission" :text="[getTransGearsDescription, getTransmissionDescription]"/>

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="Year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     name="year"
                     :options="getYearsOptions"
                     @change="onInputYear"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make,inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"/>
    </detail-row>
    </ValidationProvider>

    <auto-detail-row title="Model" v-model="vehicle.model" validation-rule="required|max:50|xml"/>

    <auto-detail-row title="Sub Model / Trim" v-model="vehicle.trim" validation-rule="max:100|xml"/>

    <auto-detail-row :title="getVehicleOverviewFeatures.rvClass.name" v-model="getVehicleOverviewFeatures.rvClass.value" :options="getNameValueOptions(getVehicleOverviewFeatures.rvClass.nameValueOptions)"/>

    <auto-detail-row title="Engine" v-model="vehicle.engine" validation-rule="max:50|xml"/>

    <auto-detail-row title="Fuel Type" v-model="vehicle.engineFuelId" :options="metadata.engineFuelOptions"/>

    <detail-row fixedPayloadWidth editMode>
      <span slot="title">Transmission:</span>
      <div slot="payload" class="d-flex" style="flex-grow: 1">
        <b-form-select v-model="vehicle.transGearsId" :options="metadata.transmissionGearsOptions"></b-form-select>
        <span class="px-2"></span>
        <b-form-select v-model="vehicle.transTypeId" :options="metadata.transmissionOptions"></b-form-select>
      </div>
    </detail-row>

  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import featuresHelper from '../../../../shared/details/featuresHelper'
import autoDetailRow from '../../helpers/autoDetailRow'

export default {
  name: 'vehicle-overview-rv',
  props: {
    mode: String
  },
  computed: {
    ...mapGetters('details', [
      'vehicle',
      'metadata',
      'rvDetails'
    ]),
    ...mapGetters('categoryData', [
      'makes',
      'years'
    ]),
    getYearsOptions () {
      return this.years.map(x => ({
        value: x,
        text: x
      }))
    },
    getMakesOptions () {
      return this.makes.map(x => ({
        value: x,
        text: x
      }))
    },
    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    },
    getVehicleOverviewFeatures () {
      return {
        rvClass: this.rvDetails.features.find(x => x.id === -7102)
      }
    },
    getTransmissionDescription () {
      return this.metadata.transmissionOptions[this.vehicle.transTypeId] || '-'
    },
    getTransGearsDescription () {
      return this.metadata.transmissionGearsOptions[this.vehicle.transGearsId] || '-'
    },
    getFuelType () {
      return this.metadata.engineFuelOptions[this.vehicle.engineFuelId] || '-'
    }
  },
  methods: {
    onInputYear (newVal) {
      this.vehicle.year = newVal.isInputMode ? newVal.text : newVal.value
    },
    onInputMakes (newVal) {
      this.vehicle.make = newVal.isInputMode ? newVal.text : newVal.value
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>
