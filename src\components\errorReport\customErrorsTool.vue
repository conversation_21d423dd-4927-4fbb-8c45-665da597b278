<template>
  <div class="m-3">
    <error-filters :filters="errorFilters" :mongoQueryCommand="mongoQueryCommand" @openMongoScriptModal="onOpenMongoScriptModal"/>
    <error-mongo-query-string :filters="errorFilters" :mongoQueryCommand="mongoQueryCommand"/>
    <div v-if="mongoErrorItems">
      <span class="mt-2 font-weight-bold">Errors Count: {{ mongoErrorItems.length }}</span>
      <b-pagination
        name="error-table-pagination"
        class="mr-1"
        align="right"
        size="sm"
        v-model="errorCurrentPage"
        :total-rows="mongoErrorItems.length"
        :per-page="errorPerPage"
        aria-controls="error-table"
      >
      </b-pagination>
      <b-table
        id="error-table"
        name="error-table"
        :items="mongoErrorItems"
        :per-page="errorPerPage"
        :current-page="errorCurrentPage"
        tbody-class="py-2 align-middle"
        responsive
        striped
      >
        <template #cell(Body)="data">
          <b-btn size="sm" @click.stop="data.toggleDetails">{{ data.detailsShowing ? "Hide" : "Show" }} Details</b-btn>
        </template>
        <template #row-details="data">
          <div v-html="data.item.Body"></div>
        </template>
      </b-table>
    </div>
    <b-alert
      variant="danger"
      class="mt-2 overflow-auto"
      show
      v-else-if="errorPollingException"
    >
      {{ errorPollingException }}
    </b-alert>
    <b-modal
      no-close-on-backdrop
      title="Processing"
      name="processing"
      :visible="isProcessingModalOpen"
      @hide="onHideProcessingModal"
    >
      <loader size="lg" />
      <template #modal-footer>
        <b-btn @click="onHideProcessingModal">Cancel</b-btn>
      </template>
    </b-modal>
    <b-modal
      title="Mongo Script"
      :visible="isMongoScriptModalOpen"
      @hide="onHideMongoScriptModal"
      size="lg"
    >
      <b-input-group prepend="Projection">
        <b-form-input v-model="mongoQueryCommand.projectionString"></b-form-input>
      </b-input-group>
      <b-input-group prepend="Sort" class="mt-1">
        <b-form-input v-model="mongoQueryCommand.sortString"></b-form-input>
      </b-input-group>
      <b-input-group prepend="Query" class="mt-1">
        <b-form-textarea v-model="mongoQueryCommand.queryString" rows="10">
        </b-form-textarea>
      </b-input-group>
      <template #modal-footer>
        <b-btn
          size="sm"
          variant="primary"
          @click="runMongoQueryString"
          :disabled="isDisabledBtn"
        >
          Submit
        </b-btn>
        <b-btn size="sm" @click="onHideMongoScriptModal">Close</b-btn>
      </template>
    </b-modal>
  </div>
</template>

<script>
import moment from 'moment-timezone'
import errorMongoQueryString from '@/components/errorReport/errorMongoQueryString'
import errorFilters from '@/components/errorReport/errorFilters'
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import constants from '@/shared/common/constants'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import { taskStatus, errorMonitorTimezone, availableFields, mongoQueryOperators, mongoProjectionOperators, mongoSortOperators } from '@/shared/errorReport/constants'
import { mapGetters } from 'vuex'

const defaultFilters = new ObjectSchema({
  errorreporttypes: { type: String, default: '' },
  dateFrom: { type: String, default: '' },
  dateTo: { type: String, default: '' }
})

export default {
  name: 'custom-errors-tool',
  data () {
    return {
      mongoQueryCommand: {
        queryString: '',
        projectionString: '',
        sortString: '',
        matches: [{
          conjugation: '$and',
          fields: [{
            name: availableFields[2],
            operator: mongoQueryOperators[2],
            input: ''
          }],
          innerMatches: []
        }],
        dateFrom: '',
        dateTo: '',
        categories: '',
        projection: [{
          field: availableFields[2],
          operator: mongoProjectionOperators[0]
        },
        {
          field: availableFields[14],
          operator: mongoProjectionOperators[0]
        },
        {
          field: availableFields[8],
          operator: mongoProjectionOperators[0]
        },
        {
          field: availableFields[11],
          operator: mongoProjectionOperators[0]
        }],
        sort: [{
          field: availableFields[8],
          operator: mongoSortOperators[1]
        }]
      },
      taskStatus,
      mongoQuery: {},
      errorFilters: defaultFilters.getObject(),
      errorCurrentPage: 1,
      errorPerPage: 10,
      isMongoScriptModalOpen: false,
      isProcessingModalOpen: false,
      isDisabledBtn: false,
      mongoErrorItems: null,
      errorPollingException: null,
      errorReportTaskId: null
    }
  },
  created () {
    this.populateFilterData()
  },
  beforeDestroy () {
    clearInterval(this.interval)
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'loader': loader,
    'detail-row': detailRow,
    'error-mongo-query-string': errorMongoQueryString,
    'error-filters': errorFilters
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {
        canManageAccountApplicationType: () => false
      }
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    }
  },
  methods: {
    populateFilterData () {
      this.redDataCookie()
      this.populateFilterDate()
    },
    populateFilterDate () {
      let dateFrom
      let dateTo
      switch (moment().tz(errorMonitorTimezone).weekday()) {
        case constants.dayOfWeek.monday:
          dateFrom = moment().tz(errorMonitorTimezone).add(-2, 'd')
          dateTo = moment().tz(errorMonitorTimezone)
          break
        default:
          dateFrom = moment().tz(errorMonitorTimezone).add(-1, 'd')
          dateTo = moment().tz(errorMonitorTimezone)
      }
      this.errorFilters.dateFrom = moment().tz(errorMonitorTimezone).set({ 'year': dateFrom.year(), 'month': dateFrom.month(), 'date': dateFrom.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0 }).format('MM/DD/YYYY HH:mm A')
      this.errorFilters.dateTo = moment().tz(errorMonitorTimezone).set({ 'year': dateTo.year(), 'month': dateTo.month(), 'date': dateTo.date(), 'hour': 6, 'minute': 30, 'seconds': 0, 'milliseconds': 0 }).format('MM/DD/YYYY HH:mm A')
    },
    setDataCookie () {
      var cookie = ['errorReport_' + this.user.userId, '=', JSON.stringify({
        errorFilters: this.errorFilters,
        mongoQueryCommand: this.mongoQueryCommand
      })].join('')
      document.cookie = cookie
    },
    redDataCookie () {
      var result = document.cookie.match(new RegExp('errorReport_' + this.user.userId + '=([^;]+)'))
      if (result) {
        var parsedResult = JSON.parse(result[1])
        if (parsedResult.errorFilters !== undefined) {
          this.errorFilters = parsedResult.errorFilters
        }
        if (parsedResult.mongoQueryCommand !== undefined) {
          this.mongoQueryCommand = parsedResult.mongoQueryCommand
        }
      }
    },
    onOpenMongoScriptModal () {
      this.isMongoScriptModalOpen = true
    },
    onHideMongoScriptModal () {
      this.isMongoScriptModalOpen = false
    },
    runMongoQueryString () {
      this.setDataCookie()
      this.isDisabledBtn = true
      let data = { queryString: this.mongoQueryCommand.queryString, projectionString: this.mongoQueryCommand.projectionString, sortString: this.mongoQueryCommand.sortString }
      this.$store.dispatch('systemTools/runMongoQueryString', data).then(res => {
        this.pollProcessingCommand(res.data.model)
        this.isProcessingModalOpen = true
        this.isMongoScriptModalOpen = false
      }).catch(ex => {
        this.$toaster.error(`Error Occurred on call api. Message: ${ex.message}`)
        this.$logger.handleError(ex, 'Error Report Exception. Method: runMongoQueryString')
      }).finally(() => { this.isDisabledBtn = false })
    },
    pollProcessingCommand (id) {
      this.cleanResults()
      this.errorReportTaskId = id
      this.interval = setInterval(function () {
        this.$store.dispatch('systemTools/pollProcessingErrorCommand', id).then(res => {
          if (res.data.model && res.data.model.errorReportTask.status !== taskStatus.inProgress) {
            if (res.data.model.errorReportTask.status === taskStatus.completed) {
              this.mongoErrorItems = JSON.parse(res.data.model.errorReportTask.result)
            } else {
              this.errorPollingException = `Result exception message:\n ${JSON.stringify(res.data.model.errorReportTask.exception)}`
            }
            this.hideModal()
          }
        }).catch(ex => {
          this.$toaster.error(`Cannot poll processing command. Ex: ${ex.message}`)
          this.$logger.handleError(ex, 'Error Report Exception. Method: pollProcessingCommand')
          clearInterval(this.interval)
          this.onHideProcessingModal()
        })
      }.bind(this), 10000)
    },
    cleanResults () {
      this.mongoErrorItems = null
      this.errorPollingException = null
    },
    hideModal () {
      this.isProcessingModalOpen = false
      this.errorReportTaskId = null
      clearInterval(this.interval)
    },
    onHideProcessingModal () {
      if (this.errorReportTaskId) {
        this.$store.dispatch('systemTools/cancelErrorReportTask', this.errorReportTaskId).then(res => {
        }).catch(ex => {
          this.$logger.handleError(ex, 'Error occurred on cancel error report task')
        }).finally(() => {
          this.hideModal()
        })
      }
    }
  }
}
</script>

<style>
pre {
  white-space: pre-wrap;
}
.daterangepicker.single.show-calendar {
  max-width: 250px;
  min-width: 250px;
}
</style>
