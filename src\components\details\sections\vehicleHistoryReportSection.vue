<template>
  <details-section title="Vehicle History Report" v-model="mode" @cancel="onCancel" v-if="getReportType.accountHistoryType && !vehicle.isNew" :disableEdit="!getReportType.isValid">
    <div class="view" v-if="!getReportType.isValid">
      <detail-row>
        <span slot="title">Report Type:</span>
        <span slot="payload">{{getReportType.name}}</span>
      </detail-row>
      <detail-row>
        <span slot="title">Details:</span>
        <span slot="payload">No {{ getReportType.name }} Vehicle History Report Exists for vehicle</span>
      </detail-row>
    </div>
    <div class="view" v-else-if="mode === 'view'">
      <detail-row title-position="start">
        <span slot="title">Report Type:</span>
        <a v-if="getReportType.externalLink" slot="payload" :href="getReportType.externalLink" target="_blank" class="vehicle-report-link d-flex align-items-center">
          <b-img v-if="getReportType.iconLink" :src="getReportType.iconLink" width="70" left class="mr-2"/>
          <span>View {{getReportType.name}} Report</span>
        </a>
        <span v-else slot="payload">{{getReportType.name}}</span>
      </detail-row>
      <detail-row v-if="getReportType.expirationDate">
        <span slot="title">Expires:</span>
        <span slot="payload">{{getFormattedDate}}</span>
      </detail-row>
      <detail-row>
        <span slot="title">Display report?</span>
        <span slot="payload">{{getDisplayReportLabel}}</span>
      </detail-row>
    </div>
    <div class="edit" v-else-if="mode === 'edit'">
      <detail-row title-position="start">
        <span slot="title">Report Type:</span>
        <div v-if="getReportType.externalLink" slot="payload">
        <a slot="payload" :href="getReportType.externalLink" target="_blank" class="vehicle-report-link d-flex align-items-center">
          <b-img v-if="getReportType.iconLink" :src="getReportType.iconLink" width="70" left class="mr-2"/>
          <span>View {{getReportType.name}} Report</span>
        </a>
        </div>
        <span v-else slot="payload">{{getReportType.name}}</span>
      </detail-row>
      <detail-row v-if="getReportType.expirationDate">
        <span slot="title">Expires:</span>
        <span slot="payload">{{getFormattedDate}}</span>
      </detail-row>
      <detail-row fixedPayloadWidth editMode>
        <span slot="title">Display report?</span>
        <b-form-checkbox slot="payload" v-model="hasToDisplayReport"></b-form-checkbox>
      </detail-row>
    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../helpers/detailRow'
import detailsSection from '@/components/details/detailsSection'
import moment from 'moment'

export default {
  name: 'vehicle-history-report-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'inventoryAccountSettings']),
    getReportType () {
      let vehicleReport = this.vehicle.reportInformation || {}
      let vehicleHistorySettings = this.inventoryAccountSettings.vehicleHistorySettings
      let vehicleReportVm = {
        name: '',
        isValid: false,
        externalLink: null,
        accountHistoryType: vehicleHistorySettings.vehicleHistoryType,
        report: null,
        expirationDate: null,
        vehicleHistoryType: vehicleReport.vehicleHistoryType,
        isAutoLink: false,
        iconLink: null
      }

      switch (vehicleHistorySettings.vehicleHistoryType) {
        case 2:
          vehicleReportVm.name = 'Autocheck'
          if (vehicleReport.autocheck) {
            vehicleReportVm.report = vehicleReport.autocheck
            vehicleReportVm.isAutoLink = vehicleHistorySettings.autocheck.isAutoLink
            if (vehicleReport.autocheck.isValidForApps) {
              vehicleReportVm.isValid = true
              vehicleReportVm.externalLink = `/api/inventory/${this.vehicle.accountId}/${this.vehicle.vin}/reports/autocheck`
              vehicleReportVm.iconLink = 'https://images.ebizautos.media/autocheck-logo.svg'
            }
          }

          break
        case 1:
          vehicleReportVm.name = 'CARFAX'
          if (vehicleReport.carfax) {
            vehicleReportVm.report = vehicleReport.carfax
            vehicleReportVm.isAutoLink = vehicleHistorySettings.carfax.isAutoLink
            vehicleReportVm.isValid = vehicleReport.carfax.isReportValid
            if (vehicleReport.carfax.isReportValid) {
              vehicleReportVm.expirationDate = vehicleReport.carfax.expirationDate
              if (vehicleReport.carfax.isOneOwner) {
                vehicleReportVm.name = 'CARFAX 1-Owner'
              }
              vehicleReportVm.externalLink = vehicleReport.carfax.linkUrl
              vehicleReportVm.iconLink = vehicleReport.carfax.iconUrl
            }
          }
          break
      }

      return vehicleReportVm
    },
    hasToDisplayReport: {
      get: function () {
        return this.getReportType.vehicleHistoryType !== 0 &&
          this.getReportType.vehicleHistoryType === this.getReportType.accountHistoryType
      },
      set: function (newValue) {
        if (newValue) {
          this.vehicle.reportInformation.vehicleHistoryType = this.getReportType.accountHistoryType
        } else {
          this.vehicle.reportInformation.vehicleHistoryType = 0
        }

        if (this.getReportType.isAutoLink) {
          this.getReportType.report.hasToBlockAutoLink = !newValue
        } else {
          this.getReportType.report.hasToBlockAutoLink = false
        }
      }
    },
    getDisplayReportLabel () {
      return this.hasToDisplayReport ? 'Yes' : 'No'
    },
    getFormattedDate () {
      return moment(this.getReportType.expirationDate).format('MM/DD/YYYY')
    }
  },
  methods: {
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'detail-row': detailRow
  }
}
</script>

<style scoped>
.vehicle-report-link {
  text-decoration: underline;
  color: #ca1713;
}
.vehicle-report-link:hover {
  text-decoration: underline;
  color: #bf0e16
}
</style>
