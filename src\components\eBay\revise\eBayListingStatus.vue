<template>
  <div>
    <h6 class="border-bottom py-2">eBay Listing Status</h6>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Listing ID:</span>
      <b-link :href="getListingUrl" slot="payload"><u>{{revise.AuctionId}}</u></b-link>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Start Time:</span>
      <span slot="payload">{{getStartTime}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Time Remaining:</span>
      <span slot="payload">{{getTimeRemaining}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Bid Status:</span>
      <span slot="payload">{{revise.BidsTotal > 0 ? revise.BidsTotal : 'No Bids'}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Current High Bid:</span>
      <span slot="payload">{{revise.HighBid > 0 ? revise.HighBid : 'No High Bid'}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Current Reserver:</span>
      <span slot="payload">{{getPriceDesc(revise.ReservePrice)}}</span>
    </detail-row>
    <detail-row :fixed-payload-width="true">
      <span slot="title">Current Start Price:</span>
      <span slot="payload">{{getPriceDesc(revise.StartPrice)}}</span>
    </detail-row>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import moment from 'moment-timezone'
import {mapGetters} from 'vuex'
import numeral from 'numeral'

export default {
  components: {
    'detail-row': detailRow
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise']),
    getTimeRemaining () {
      let duration = moment.duration(moment(this.revise.EndDateTime).diff(moment()))
      let days = parseInt(duration.asDays())
      let hours = parseInt(duration.asHours() - parseInt(duration.asHours() / 24) * 24)
      let minutes = parseInt(duration.asMinutes() - parseInt(duration.asMinutes() / 60) * 60)

      return `${days} ${days > 1 ? 'Days' : 'Day'}  ${hours} ${hours > 1 ? 'Hours' : 'Hour'} ${minutes} ${minutes > 1 ? 'Minutes' : 'Minute'}`
    },
    getStartTime () {
      return moment(this.revise.StartDateTime).tz('America/Los_Angeles').format('MMM-DD-YY hh:mm:ss z') + ` - ${this.revise.AuctionDuration} Day Duration`
    },
    getListingUrl () {
      return constants.eBayInfoUrls.ebayItemUrl(this.revise.AuctionId)
    }
  },
  methods: {
    getPriceDesc (value) {
      return numeral(value).format('$0,0')
    }
  }
}
</script>
