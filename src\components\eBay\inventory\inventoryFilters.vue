<template>
  <div class="position-relative">
    <paging
      class="custom-ebay-inventory-paging d-none d-md-block"
      :pageNumber="pageNumber"
      :pageSize="pageSize"
      :totalItems="totalItems"
      @numberChanged="onPageNumberChanged"
      @changePageSize="onPageSizeChanged"
    />

    <b-tabs v-model="selectedEBayStatus" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="(status, index) in inventoryFilters.eBayStatus.options" :key="`${index}`" :title="`${status.text}`">
        <b-form inline @submit.prevent="onSubmitSearch">
          <b-card-body>
            <div class="form-row">
              <b-col lg="5" md="7" sm="9" xs="12">
                <b-input-group class="inventory-filter__search">
                  <b-form-input placeholder="Search by VIN, Stock or Make/Model" max='200' v-model.trim="inventoryFilters.search.value"></b-form-input>
                  <b-input-group-append>
                    <b-btn type="submit" variant="primary">Submit</b-btn>
                  </b-input-group-append>
                </b-input-group>
              </b-col>

              <b-col lg="5" md="5" sm="3" xs="12" class="text-right mt-3" offset-lg="2">
                <b-link class="toggle-search-filters-link d-none d-sm-block"  v-b-toggle.advanced-search-collapse>
                  <span class="when-opened">
                    Basic search
                  </span>
                  <span class="when-closed">
                    Advanced search
                  </span>
                </b-link>
                <b-btn class="w-100 d-block d-sm-none"  v-b-toggle.advanced-search-collapse>
                  <span class="when-opened">
                    Basic search
                  </span>
                  <span class="when-closed">
                    Advanced search
                  </span>
                </b-btn>
              </b-col>

            </div>
          </b-card-body>
        </b-form>

        <b-collapse v-model="isAdvancedFiltersVisible" id="advanced-search-collapse">
          <div class="border-top ml-4 mr-4"></div>
          <b-form v-on:submit.prevent="onFilterApply">
          <b-card-body>
            <div class="form-row">
              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.condition.value"
                    :options="inventoryFilters.condition.options"
                    @change="onSelectChange($event, inventoryFilters.condition.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.vehicleType.value"
                    text-field="Text"
                    value-field="Value"
                    :options="inventoryFilters.vehicleType.options"
                    @change="onSelectChange($event, inventoryFilters.vehicleType.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.bodyStyle.value"
                    :options="inventoryFilters.bodyStyle.options"
                    @change="onSelectChange($event, inventoryFilters.bodyStyle.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.make.value"
                    :options="inventoryFilters.make.options"
                    @change="onSelectChange($event, inventoryFilters.make.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>
            </div>

            <div class="form-row">
              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.daysInStockFrom.value"
                    :options="inventoryFilters.daysInStockFrom.options"
                    @change="onSelectChange($event, inventoryFilters.daysInStockFrom.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.daysInStockTo.value"
                    :options="inventoryFilters.daysInStockTo.options"
                    @change="onSelectChange($event, inventoryFilters.daysInStockTo.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-input-group size="md" prepend="$">
                    <b-form-input
                      max='200'
                      v-model="inventoryFilters.priceFrom.value"
                      placeholder="Enter Price To">
                    </b-form-input>
                  </b-input-group>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-input-group size="md" prepend="$">
                    <b-form-input
                      max='200'
                      v-model="inventoryFilters.priceTo.value"
                      placeholder="Enter Price To">
                    </b-form-input>
                  </b-input-group>
                </b-form-group>
              </b-col>
            </div>

            <div class="form-row">
              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.mileageFrom.value"
                    :options="inventoryFilters.mileageFrom.options"
                    @change="onSelectChange($event, inventoryFilters.mileageFrom.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.mileageTo.value"
                    :options="inventoryFilters.mileageTo.options"
                    @change="onSelectChange($event, inventoryFilters.mileageTo.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    text-field="Text"
                    value-field="Value"
                    v-model="inventoryFilters.photoCount.value"
                    :options="inventoryFilters.photoCount.options"
                    @change="onSelectChange($event, inventoryFilters.photoCount.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <div class="flex-fill">
                    <b-btn class="btn-block" variant="primary" type="submit">Search</b-btn>
                  </div>
                </b-form-group>
              </b-col>
            </div>
          </b-card-body>
        </b-form>
        </b-collapse>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
import inventoryFiltersMixin from './../../../mixins/eBay/inventoryFiltersMixin'
import paging from './../../../components/_shared/paging.vue'
import defaultEBayInventoryFilters from './../../../shared/ebay/inventoryFilters'
import lodash from 'lodash'
import constants from '@/shared/ebay/constants'

export default {
  name: 'inventory-filters',
  components: {
    'paging': paging
  },
  mixins: [inventoryFiltersMixin],
  props: {
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    filters: { type: Object, required: true },
    filtersOptions: { type: Object, required: true }
  },
  data: function () {
    const inventoryFilters = this.buildInventoryFilters()

    return {
      inventoryFilters: inventoryFilters,
      isAdvancedFiltersVisible: this.isAdvantageSearchUnfolded(inventoryFilters)
    }
  },
  computed: {
    selectedEBayStatus: {
      get: function () {
        let index = this.inventoryFilters.eBayStatus.options.findIndex(x => x.value === this.filters.status)
        if (index >= 0) {
          return index
        }

        return 0
      },
      set: function (index) {
        const filters = lodash.assign({},
          defaultEBayInventoryFilters.objKeyValues,
          { [this.inventoryFilters.eBayStatus.filterName]: this.inventoryFilters.eBayStatus.options[index].value }
        )
        this.onFiltersChange(filters, false)
        this.isAdvancedFiltersVisible = false
      }
    }
  },
  watch: {
    filtersOptions: {
      deep: true,
      immediate: true,
      handler () {
        this.inventoryFilters = this.buildInventoryFilters()
      }
    }
  },
  methods: {
    buildInventoryFilters () {
      return {
        search: {
          filterName: 'search',
          value: this.filters.search
        },
        make: {
          filterName: 'make',
          options: Array.concat([{Value: '', Text: 'Select Make'}], Object.values(this.filtersOptions.Makes)),
          value: this.filters.make
        },
        condition: {
          filterName: 'condition',
          options: Array.concat([{Value: 0, Text: 'Select Condition'}], Object.values(this.filtersOptions.Conditions)),
          value: this.filters.condition
        },
        mileageFrom: {
          filterName: 'mileageFrom',
          options: Array.concat([{Value: -1, Text: 'Select Mileage From'}], Object.values(this.filtersOptions.MileagesFrom)),
          value: this.filters.mileageFrom
        },
        mileageTo: {
          filterName: 'mileageTo',
          options: Array.concat([{Value: -1, Text: 'Select Mileage To'}], Object.values(this.filtersOptions.MileagesTo)),
          value: this.filters.mileageTo
        },
        priceFrom: {
          filterName: 'priceFrom',
          value: this.filters.priceFrom
        },
        priceTo: {
          filterName: 'priceTo',
          value: this.filters.priceTo
        },
        daysInStockFrom: {
          filterName: 'daysFrom',
          options: Array.concat([{Value: -1, Text: 'Select Days in Stock From'}], Object.values(this.filtersOptions.DaysInStockFrom)),
          value: this.filters.daysFrom
        },
        daysInStockTo: {
          filterName: 'daysTo',
          options: Array.concat([{Value: -1, Text: 'Select Days in Stock To'}], Object.values(this.filtersOptions.DaysInStockTo)),
          value: this.filters.daysTo
        },
        photoCount: {
          filterName: 'photosFrom',
          options: Array.concat([{Value: -1, Text: 'Select Photos From'}], Object.values(this.filtersOptions.PhotosCount)),
          value: this.filters.photosFrom
        },
        eBayStatus: {
          filterName: 'status',
          options: constants.eBayInventoryStatusOptions
        },
        vehicleType: {
          filterName: 'vehicleType',
          options: Array.concat([{Value: 0, Text: 'Select Vehicle Type'}], Object.values(this.filtersOptions.VehicleTypes)),
          value: this.filters.vehicleType
        },
        bodyStyle: {
          filterName: 'bodyStyle',
          options: Array.concat([{Value: 0, Text: 'Select Body Style'}], Object.values(this.filtersOptions.BodyStyles)),
          value: this.filters.bodyStyle
        }
      }
    },
    onFiltersChange (filterNameValues, hasToResetPageNumber = true) {
      if (hasToResetPageNumber) {
        filterNameValues['page'] = 1
      }
      this.$emit('filtersChanged', filterNameValues)
    },
    onSubmitSearch () {
      const filters = {}
      if (!this.isAdvancedFiltersVisible) {
        const defaultKeyValues = lodash.assign({}, defaultEBayInventoryFilters.objKeyValues)
        delete defaultKeyValues.status // to avoid resent current active tab
        lodash.assign(filters, defaultKeyValues)
      }
      lodash.assign(filters, {[this.inventoryFilters.search.filterName]: this.inventoryFilters.search.value})

      this.onFiltersChange(filters)
    },
    onFilterApply () {
      this.onFiltersChange({
        [this.inventoryFilters.priceFrom.filterName]: this.inventoryFilters.priceFrom.value,
        [this.inventoryFilters.priceTo.filterName]: this.inventoryFilters.priceTo.value
      })
    },
    onSelectChange (value, filterName) {
      this.onFiltersChange({
        [filterName]: value
      })
    },
    isAdvantageSearchUnfolded (inventoryFilters) {
      return (inventoryFilters.condition.value && inventoryFilters.condition.value !== defaultEBayInventoryFilters.objKeyValues.condition) ||
        (inventoryFilters.vehicleType.value && inventoryFilters.vehicleType.value !== defaultEBayInventoryFilters.objKeyValues.vehicleType) ||
        (inventoryFilters.bodyStyle.value && inventoryFilters.bodyStyle.value !== defaultEBayInventoryFilters.objKeyValues.bodyStyle) ||
        (inventoryFilters.make.value && inventoryFilters.make.value !== defaultEBayInventoryFilters.objKeyValues.make) ||
        (inventoryFilters.daysInStockFrom.value && inventoryFilters.daysInStockFrom.value !== defaultEBayInventoryFilters.objKeyValues.daysFrom) ||
        (inventoryFilters.daysInStockTo.value && inventoryFilters.daysInStockTo.value !== defaultEBayInventoryFilters.objKeyValues.daysTo) ||
        (inventoryFilters.priceFrom.value && inventoryFilters.priceFrom.value !== defaultEBayInventoryFilters.objKeyValues.priceFrom) ||
        (inventoryFilters.priceTo.value && inventoryFilters.priceTo.value !== defaultEBayInventoryFilters.objKeyValues.priceTo) ||
        (inventoryFilters.mileageFrom.value && inventoryFilters.mileageFrom.value !== defaultEBayInventoryFilters.objKeyValues.mileageFrom) ||
        (inventoryFilters.mileageTo.value && inventoryFilters.mileageTo.value !== defaultEBayInventoryFilters.objKeyValues.mileageTo) ||
        (inventoryFilters.photoCount.value && inventoryFilters.photoCount.value !== defaultEBayInventoryFilters.objKeyValues.photosFrom)
    }
  }
}
</script>

<style lang="scss">
.custom-ebay-inventory-paging {
  position: absolute;
  right: -10px;
  top: -15px;
  z-index: 2;
}
.collapsed > .when-opened,
:not(.collapsed) > .when-closed {
  display: none;
}
.toggle-search-filters-link {
  text-decoration: underline;
}
@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
    flex-wrap: nowrap!important;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    border: 0;
    overflow-x: scroll;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}
</style>
