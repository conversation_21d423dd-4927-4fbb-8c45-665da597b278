<template>
  <div>
    <b-row>
      <b-col>
        <h4>Revise eBay Listings</h4>
      </b-col>
      <b-col>
        <c-button class="float-right" :message="`Are you sure you want to revise?`" variant="primary" size="md" @confirm="onConfirm">Revise eBay</c-button>
      </b-col>
    </b-row>
    <edit :accountId="revise.AccountId" :vin="revise.Vin" isEBay/>
    <b-modal
      :visible="isVisible"
      centered
      hide-footer
      hide-header-close
      hide-header
      no-close-on-backdrop
      no-close-on-esc
    >
      <loader class="m-4" type="wave" size="md"/>
    </b-modal>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import loader from '@/components/_shared/loader'
import edit from '@/pages/detail/edit'
import constants from '@/shared/ebay/constants'
import { mapGetters } from 'vuex'

export default {
  props: {
    btnDesc: {
      type: String,
      default: 'Revise'
    },
    reviseHeader: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isVisible: false,
      statuses: constants.eBayVehicleSynchronizationStatuses
    }
  },
  components: {
    detailRow,
    edit,
    loader
  },
  computed: {
    ...mapGetters('eBayRevise', ['revise'])
  },
  methods: {
    onConfirm () {
      this.isVisible = true
      this.$store.dispatch('eBay/synchronizeVehicle', { accountId: this.revise.AccountId, vin: this.revise.Vin }).then(res => {
        setTimeout(() => this.checkSynchronizeStatus(res.data.correlationId), 10000)
      }).catch(ex => {
        this.isVisible = false
        this.$toaster.error('Something went wrong!')
        this.$logger.handleError(ex, 'Cannot revise Vehicle Description')
      })
    },
    modifyVehicleDescription () {
      let apiParams = {
        accountId: this.revise.AccountId,
        auctionId: this.revise.AuctionId,
        data: { vin: this.revise.Vin }
      }

      this.$store.dispatch('eBayRevise/modifyVehicleDescription', apiParams).then(res => {
        this.$toaster.success(`Revised Successfully`)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, `Exception occurred on ${this.reviseHeader}`)
        }
      }).finally(() => {
        this.isVisible = false
      })
    },
    async checkSynchronizeStatus (correlationId) {
      let isProcessing = true
      try {
        let res = await this.$store.dispatch('eBay/checkSynchronizeVehicleStatus', { accountId: this.revise.AccountId, vin: this.revise.Vin, filter: { correlationId: correlationId } })
        if (res.data.Status === this.statuses.completed) {
          this.modifyVehicleDescription()
          isProcessing = false
        } else if (res.data.Status === this.statuses.expired || res.data.Status === this.statuses.failed) {
          this.$toaster.error('Something went wrong!')
          isProcessing = false
          this.isVisible = false
        }
      } catch (ex) {
        this.$toaster.error('Something went wrong!')
        isProcessing = false
        this.isVisible = false
      }

      if (isProcessing) {
        setTimeout(() => this.checkSynchronizeStatus(correlationId), 10000)
      }
    }
  }
}
</script>
