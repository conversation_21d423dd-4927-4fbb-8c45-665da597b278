version: 0.2
phases:
  pre_build:
    commands:
      - echo Restore npm packages on `date`
      - npm --production=false install
  build:
    commands:
      - echo Build started on `date`
      - |
        if [ $ENVIRONMENT_NAME = "Production" ]; then
          npm run livebuild
          cp web.live.config dist/web.config
        else
          npm run sandbuild
          cp web.sandbox.config dist/web.config
        fi
      - mkdir -p build_output/artifacts
      - cp -a dist/. build_output/artifacts
      - cp -a aws/deploy/. build_output
artifacts:
  files:
    - '**/*'
  base-directory: build_output
  discard-paths: no
