<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Additional Info / Trim" :text="vehicle.trim" />

    <auto-detail-row title="Engine Details" :text="vehicle.engine"/>

    <auto-detail-row title="Engine Size (cc)" :text="vehicle.eBayEngineSize"/>

    <auto-detail-row :title="vehicleOverviewFeatures.motorcycleType.name" :text="getSelectedAttributeOption(vehicleOverviewFeatures.motorcycleType).key"/>

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="Year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     :options="getYearsOptions"
                     @change="onInputYear"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make,inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"/>
    </detail-row>
    </ValidationProvider>

    <auto-detail-row title="Model" v-model="vehicle.model" validation-rule="required|max:50|xml"/>

    <auto-detail-row title="Additional Info / Trim" v-model="vehicle.trim" validation-rule="max:100|xml"/>

    <auto-detail-row title="Engine" v-model="vehicle.engine" validation-rule="max:500|xml"/>

    <auto-detail-row title="Engine Size (cc)" v-model="vehicle.eBayEngineSize" validation-rule="max:50|xml"/>

    <auto-detail-row :title="vehicleOverviewFeatures.motorcycleType.name" v-model="vehicleOverviewFeatures.motorcycleType.value" :options="getNameValueOptions(vehicleOverviewFeatures.motorcycleType.nameValueOptions)"/>

  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import autoDetailRow from '../../helpers/autoDetailRow'
import featuresHelper from '../../../../shared/details/featuresHelper'

export default {
  name: 'vehicle-overview-motorcycle',
  props: {
    mode: String
  },
  computed: {
    ...mapGetters('details', [
      'vehicle',
      'metadata',
      'motorcycleUpgrades'
    ]),
    ...mapGetters('categoryData', [
      'makes',
      'years'
    ]),
    vehicleOverviewFeatures () {
      return {
        motorcycleType: this.motorcycleUpgrades.features.find(x => x.id === -4053)
      }
    },
    getYearsOptions () {
      return this.years.map(x => ({
        value: x,
        text: x
      }))
    },
    getMakesOptions () {
      return this.makes.map(x => ({
        value: x,
        text: x
      }))
    },
    getTransOptions () {
      return {
        0: 'Select',
        ...this.metadata.transmissionOptions
      }
    },
    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    }
  },
  methods: {
    onInputYear (newVal) {
      this.vehicle.year = newVal.isInputMode ? newVal.text : newVal.value
    },
    onInputMakes (newVal) {
      this.vehicle.make = newVal.isInputMode ? newVal.text : newVal.value
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>
