<template>
  <div>
    <div class="mb-3">
      <turnstile-plugin-section
        :isUpdatingProcessed="sections.turnstilePlugin.isUpdatingProcessed"
        :isDisabled="sections.turnstilePlugin.isDisabled"
        @save="saveTurnstilePluginSettings"/>
    </div>
  </div>
</template>

<script>
import turnstilePluginSection from './turnstileSettingsSections/turnstilePluginSection'
import permissions from '@/shared/common/permissions'
import { mapGetters } from 'vuex'

export default {
  name: 'turnstile-settings',
  data () {
    return {
      isDisabled: false,
      accountId: +this.$route.params.accountId,
      sections: {
        turnstilePlugin: { isDisabled: false, isUpdatingProcessed: false }
      }
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasPermissionsToManageDealerSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageDealerSettings)
    }
  },
  components: {
    'turnstile-plugin-section': turnstilePluginSection
  },
  methods: {
    saveTurnstilePluginSettings (settings) {
      this.sections.turnstilePlugin.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('turnstilePlugin')
      this.saveTurnstileSettings(settings)
    },
    saveTurnstileSettings (data) {
      this.$store.dispatch('leadsAccountSettings/updateTurnstileSettings', { accountId: this.accountId, data: data }).then(() => {
        this.$toaster.success('Turnstile settings saved successfully')
      }).catch(ex => {
        if (ex.response) {
          this.$toaster.error(`Cannot update Turnstile settings. Message ${ex.response.data}`)
        } else {
          this.$toaster.error(`Cannot update Turnstile settings. Message ${ex.message}`)
        }
        this.$logger.handleError(ex, `Cannot update Turnstile settings for accountId: ${this.accountId}`)
      }).finally(() => {
        this.setSectionsToDefault()
        this.$emit('refreshSettings')
      })
    },
    makeOtherSectionsDisabled (excludeSectionKey) {
      Object.keys(this.sections).filter(key => key !== excludeSectionKey).forEach(key => {
        this.sections[key].isDisabled = true
      })
    },
    setSectionsToDefault () {
      Object.values(this.sections).forEach(section => {
        section.isDisabled = false
        section.isUpdatingProcessed = false
      })
    }
  }
}
</script>
