<template>
  <div class="p-4">
    <div class="mb-4">
      <h5>Turnstile Plugin Settings</h5>
      <p class="text-muted">Configure Cloudflare Turnstile settings for lead form protection.</p>
    </div>

    <div>
      <b-form @submit.prevent="onSave">
        <!-- Enable Turnstile -->
        <b-form-group label="Enable Turnstile" label-for="turnstile-enabled">
          <b-form-checkbox
            id="turnstile-enabled"
            v-model="localSettings.isTurnstileEnabled"
            :disabled="isUpdating"
          >
            Enable Turnstile protection for lead forms
          </b-form-checkbox>
        </b-form-group>

        <!-- Site Key -->
        <b-form-group
          label="Site Key"
          label-for="site-key"
          :invalid-feedback="errors.siteKey"
          :state="!errors.siteKey"
        >
          <b-form-input
            id="site-key"
            v-model="localSettings.siteKey"
            :disabled="isUpdating || !localSettings.isTurnstileEnabled"
            placeholder="Enter your Turnstile site key"
            :state="!errors.siteKey"
          />
        </b-form-group>

        <!-- Secret Key -->
        <b-form-group
          label="Secret Key"
          label-for="secret-key"
          :invalid-feedback="errors.secretKey"
          :state="!errors.secretKey"
        >
          <b-form-input
            id="secret-key"
            v-model="localSettings.secretKey"
            :disabled="isUpdating || !localSettings.isTurnstileEnabled"
            placeholder="Enter your Turnstile secret key"
            type="password"
            :state="!errors.secretKey"
          />
        </b-form-group>

        <!-- Failure Message -->
        <b-form-group
          label="Failure Message"
          label-for="failure-message"
          description="Message to display when Turnstile verification fails"
        >
          <b-form-textarea
            id="failure-message"
            v-model="localSettings.failureMessage"
            :disabled="isUpdating || !localSettings.isTurnstileEnabled"
            placeholder="Enter failure message"
            rows="3"
          />
        </b-form-group>

        <!-- Failure Action -->
        <b-form-group
          label="Failure Action"
          label-for="failure-action"
          description="What to do when Turnstile verification fails"
        >
          <b-form-select
            id="failure-action"
            v-model="localSettings.failureAction"
            :disabled="isUpdating || !localSettings.isTurnstileEnabled"
            :options="failureActionOptions"
          />
        </b-form-group>

        <!-- Lead Type Settings -->
        <div v-if="localSettings.isTurnstileEnabled" class="mt-4">
          <h6>Lead Type Settings</h6>
          <p class="text-muted small">Configure which lead types should use Turnstile protection (only applicable lead types are shown)</p>

          <div class="border rounded p-3">
            <b-form-checkbox-group
              v-model="activeLeadTypes"
              :options="leadTypeOptions"
              stacked
              :disabled="isUpdating"
            />
          </div>
        </div>

        <!-- Save Button -->
        <div class="mt-4">
          <b-button
            type="submit"
            variant="primary"
            :disabled="isUpdating || !hasChanges"
            class="mr-2"
          >
            <b-spinner v-if="isUpdating" small class="mr-1"/>
            {{ isUpdating ? 'Saving...' : 'Save Settings' }}
          </b-button>

          <b-button
            variant="secondary"
            :disabled="isUpdating"
            @click="onReset"
          >
            Reset
          </b-button>
        </div>
      </b-form>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { turnstileFailureAction, turnstileAllowedLeadTypes } from '@/shared/leads/common'
import permissions from '@/shared/common/permissions'

export default {
  name: 'turnstile-settings',
  props: {
    settings: { type: Object, default: null }
  },
  data () {
    return {
      isUpdating: false,
      localSettings: {
        accountId: 0,
        isTurnstileEnabled: false,
        siteKey: '',
        secretKey: '',
        failureMessage: '',
        failureAction: 0,
        leadTypeSettings: []
      },
      originalSettings: null,
      errors: {}
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasPermissionsToManageDealerSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageDealerSettings)
    },
    accountId () {
      return +this.$route.params.accountId
    },
    failureActionOptions () {
      return Object.values(turnstileFailureAction).map(action => ({
        value: action.value,
        text: action.label
      }))
    },
    leadTypeOptions () {
      return Object.values(turnstileAllowedLeadTypes).map(type => ({
        value: type.value,
        text: type.label
      }))
    },
    activeLeadTypes: {
      get () {
        return this.localSettings.leadTypeSettings
          .filter(setting => setting.isActive)
          .map(setting => setting.leadType)
      },
      set (values) {
        this.localSettings.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: values.includes(type.value)
        }))
      }
    },
    hasChanges () {
      return JSON.stringify(this.localSettings) !== JSON.stringify(this.originalSettings)
    }
  },
  watch: {
    settings: {
      handler (newSettings) {
        this.initializeSettings(newSettings)
      },
      immediate: true
    }
  },
  methods: {
    initializeSettings (settings) {
      if (settings) {
        this.localSettings = { ...settings }
        this.originalSettings = { ...settings }
      } else {
        // Initialize with default values
        this.localSettings.accountId = this.accountId
        this.localSettings.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: false
        }))
        this.originalSettings = { ...this.localSettings }
      }
    },
    async onSave () {
      if (!this.validateSettings()) {
        return
      }

      try {
        this.isUpdating = true
        await this.$store.dispatch('leadsAccountSettings/updateTurnstileSettings', {
          accountId: this.accountId,
          data: this.localSettings
        })

        this.originalSettings = { ...this.localSettings }
        this.$toaster.success('Turnstile settings saved successfully')
        this.$emit('reload')
      } catch (error) {
        this.$toaster.error('Failed to save Turnstile settings')
        this.$logger.handleError(error, 'Failed to save Turnstile settings')
      } finally {
        this.isUpdating = false
      }
    },
    onReset () {
      this.localSettings = { ...this.originalSettings }
      this.errors = {}
    },
    validateSettings () {
      this.errors = {}

      if (this.localSettings.isTurnstileEnabled) {
        if (!this.localSettings.siteKey || this.localSettings.siteKey.trim() === '') {
          this.errors.siteKey = 'Site key is required when Turnstile is enabled'
        }

        if (!this.localSettings.secretKey || this.localSettings.secretKey.trim() === '') {
          this.errors.secretKey = 'Secret key is required when Turnstile is enabled'
        }
      }

      return Object.keys(this.errors).length === 0
    }
  }
}
</script>
