<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Turnstile Plugin Settings" :isDisabled="isDisabled" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">Enable Turnstile:</span>
          <b-form-checkbox v-model='localSettings.isTurnstileEnabled' slot="payload" :disabled='isViewMode'>
            Enable Turnstile protection for lead forms
          </b-form-checkbox>
        </detail-row>
        <div v-if='localSettings.isTurnstileEnabled'>
          <detail-row fixedPayloadWidth>
            <span slot="title">Site Key:</span>
            <b-form-input v-model='localSettings.siteKey' placeholder="Enter your Turnstile site key" type="text" slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Secret Key:</span>
            <b-form-input v-model='localSettings.secretKey' placeholder="Enter your Turnstile secret key" type="password" slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row extraLargePayloadWidth>
            <span slot="title">Failure Message:</span>
            <b-form-textarea v-model='localSettings.failureMessage' placeholder="Message to display when Turnstile verification fails" slot="payload" :disabled='isViewMode' rows="3"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Failure Action:</span>
            <b-form-select v-model='localSettings.failureAction' :options='failureActionOptions' slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row extraLargePayloadWidth>
            <span slot="title">Lead Types:</span>
            <div slot="payload">
              <small class="text-muted d-block mb-2">Configure which lead types should use Turnstile protection</small>
              <b-form-checkbox-group
                v-model="activeLeadTypes"
                :options="leadTypeOptions"
                stacked
                :disabled="isViewMode"
              />
            </div>
          </detail-row>
        </div>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { mapGetters } from 'vuex'
import { turnstileFailureAction, turnstileAllowedLeadTypes } from '@/shared/leads/common'
import globals from '../../../globals'

export default {
  name: 'turnstile-settings',
  data () {
    return {
      isViewMode: true,
      isUpdatingProcessed: false,
      isDisabled: false,
      localSettings: {
        accountId: 0,
        isTurnstileEnabled: false,
        siteKey: '',
        secretKey: '',
        failureMessage: '',
        failureAction: 0,
        leadTypeSettings: []
      }
    }
  },
  created () {
    this.initData()
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    ...mapGetters('leadsAccountSettings', ['turnstileSettings']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    accountId () {
      return +this.$route.params.accountId
    },
    failureActionOptions () {
      return Object.values(turnstileFailureAction).map(action => ({
        value: action.value,
        text: action.label
      }))
    },
    leadTypeOptions () {
      return Object.values(turnstileAllowedLeadTypes).map(type => ({
        value: type.value,
        text: type.label
      }))
    },
    activeLeadTypes: {
      get () {
        if (!this.localSettings.leadTypeSettings) return []
        return this.localSettings.leadTypeSettings
          .filter(setting => setting.isActive)
          .map(setting => setting.leadType)
      },
      set (values) {
        this.localSettings.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: values.includes(type.value)
        }))
      }
    }
  },
  watch: {
    turnstileSettings: {
      handler () {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    initData () {
      if (this.turnstileSettings) {
        this.localSettings = globals().getClonedValue(this.turnstileSettings)
      } else {
        // Initialize with default values
        this.localSettings.accountId = this.accountId
        this.localSettings.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: false
        }))
      }
    },
    saveSettings () {
      if (this.validate()) {
        this.isUpdatingProcessed = true
        this.$store.dispatch('leadsAccountSettings/updateTurnstileSettings', {
          accountId: this.accountId,
          data: this.localSettings
        }).then(() => {
          this.$toaster.success('Turnstile settings saved successfully')
          this.isViewMode = true
          this.$emit('reload')
        }).catch(ex => {
          if (ex.response) {
            this.$toaster.error(`Cannot update Turnstile settings. Message ${ex.response.data}`)
          } else {
            this.$toaster.error(`Cannot update Turnstile settings. Message ${ex.message}`)
          }
          this.$logger.handleError(ex, `Cannot update Turnstile settings for accountId: ${this.accountId}`)
        }).finally(() => {
          this.isUpdatingProcessed = false
        })
      }
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    },
    validate () {
      if (this.localSettings.isTurnstileEnabled) {
        if (!this.localSettings.siteKey || this.localSettings.siteKey.trim() === '') {
          this.$toaster.error('Site Key field is required when Turnstile is enabled')
          return false
        }
        if (!this.localSettings.secretKey || this.localSettings.secretKey.trim() === '') {
          this.$toaster.error('Secret Key field is required when Turnstile is enabled')
          return false
        }
      }
      return true
    }
  }
}
</script>
