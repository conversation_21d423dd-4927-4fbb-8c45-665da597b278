<template>
  <div class="mb-4">
    <editSettingsHelper
      @cancel="cancel"
      @save="saveSettings"
      @changeMode="changeMode"
      title="Turnstile Plugin"
      :isLoading="isUpdating"
      :isDisabled="false"
      :isViewMode="isViewMode"
    >
      <div slot="settings-content">
        <!-- Enable Turnstile -->
        <detail-row fixedPayloadWidth>
          <span slot="title">Enable Turnstile:</span>
          <div slot="payload">
            <b-form-checkbox
              v-model="localSettings.isTurnstileEnabled"
              :disabled="isViewMode"
              class="align-middle d-inline-flex"
              style="margin:0; padding-left: 20px;"
            >
              Enable Turnstile protection for lead forms
            </b-form-checkbox>
          </div>
        </detail-row>

        <!-- Site Key -->
        <detail-row fixedPayloadWidth :error="errors.siteKey">
          <span slot="title">Site Key:</span>
          <div slot="payload">
            <b-form-input
              v-model="localSettings.siteKey"
              :disabled="isViewMode || !localSettings.isTurnstileEnabled"
              placeholder="Enter your Turnstile site key"
              :state="!errors.siteKey"
            />
          </div>
        </detail-row>

        <!-- Secret Key -->
        <detail-row fixedPayloadWidth :error="errors.secretKey">
          <span slot="title">Secret Key:</span>
          <div slot="payload">
            <b-form-input
              v-model="localSettings.secretKey"
              :disabled="isViewMode || !localSettings.isTurnstileEnabled"
              placeholder="Enter your Turnstile secret key"
              type="password"
              :state="!errors.secretKey"
            />
          </div>
        </detail-row>

        <!-- Failure Message -->
        <detail-row extraLargePayloadWidth>
          <span slot="title">Failure Message:</span>
          <div slot="payload">
            <b-form-textarea
              v-model="localSettings.failureMessage"
              :disabled="isViewMode || !localSettings.isTurnstileEnabled"
              placeholder="Message to display when Turnstile verification fails"
              rows="3"
            />
          </div>
        </detail-row>

        <!-- Failure Action -->
        <detail-row fixedPayloadWidth>
          <span slot="title">Failure Action:</span>
          <div slot="payload">
            <b-form-select
              v-model="localSettings.failureAction"
              :disabled="isViewMode || !localSettings.isTurnstileEnabled"
              :options="failureActionOptions"
            />
            <small class="text-muted d-block mt-1">What to do when Turnstile verification fails</small>
          </div>
        </detail-row>

        <!-- Lead Type Settings -->
        <detail-row v-if="localSettings.isTurnstileEnabled" extraLargePayloadWidth>
          <span slot="title">Lead Types:</span>
          <div slot="payload">
            <div class="border rounded p-3">
              <small class="text-muted d-block mb-2">Configure which lead types should use Turnstile protection (only applicable lead types are shown)</small>
              <b-form-checkbox-group
                v-model="activeLeadTypes"
                :options="leadTypeOptions"
                stacked
                :disabled="isViewMode"
              />
            </div>
          </div>
        </detail-row>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { turnstileFailureAction, turnstileAllowedLeadTypes } from '@/shared/leads/common'
import permissions from '@/shared/common/permissions'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '../../../globals'

export default {
  name: 'turnstile-settings',
  props: {
    settings: { type: Object, default: null },
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  data () {
    return {
      isViewMode: true,
      localSettings: {
        accountId: 0,
        isTurnstileEnabled: false,
        siteKey: '',
        secretKey: '',
        failureMessage: '',
        failureAction: 0,
        leadTypeSettings: []
      },
      originalSettings: null,
      errors: {}
    }
  },
  created () {
    this.initData()
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    user () {
      return (this.userInfo || {}).user || {}
    },
    hasPermissionsToManageDealerSettings () {
      return this.user && this.user.hasPermissions && this.user.hasPermissions(permissions.LeadsManageDealerSettings)
    },
    accountId () {
      return +this.$route.params.accountId
    },
    failureActionOptions () {
      return Object.values(turnstileFailureAction).map(action => ({
        value: action.value,
        text: action.label
      }))
    },
    leadTypeOptions () {
      return Object.values(turnstileAllowedLeadTypes).map(type => ({
        value: type.value,
        text: type.label
      }))
    },
    activeLeadTypes: {
      get () {
        return this.localSettings.leadTypeSettings
          .filter(setting => setting.isActive)
          .map(setting => setting.leadType)
      },
      set (values) {
        this.localSettings.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: values.includes(type.value)
        }))
      }
    },
    hasChanges () {
      return JSON.stringify(this.localSettings) !== JSON.stringify(this.originalSettings)
    },
    isUpdating () {
      return this.isUpdatingProcessed
    }
  },
  watch: {
    settings: {
      handler () {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    initData () {
      if (this.settings) {
        this.localSettings = globals().getClonedValue(this.settings)
        this.originalSettings = globals().getClonedValue(this.settings)
      } else {
        // Initialize with default values
        this.localSettings.accountId = this.accountId
        this.localSettings.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: false
        }))
        this.originalSettings = globals().getClonedValue(this.localSettings)
      }
    },
    saveSettings () {
      if (!this.validateSettings()) {
        return
      }
      this.$emit('save', this.localSettings)
      this.isViewMode = true
    },
    changeMode (mode) {
      this.isViewMode = mode
      this.errors = {}
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    },
    validateSettings () {
      this.errors = {}

      if (this.localSettings.isTurnstileEnabled) {
        if (!this.localSettings.siteKey || this.localSettings.siteKey.trim() === '') {
          this.errors.siteKey = 'Site key is required when Turnstile is enabled'
        }

        if (!this.localSettings.secretKey || this.localSettings.secretKey.trim() === '') {
          this.errors.secretKey = 'Secret key is required when Turnstile is enabled'
        }
      }

      return Object.keys(this.errors).length === 0
    }
  }
}
</script>
