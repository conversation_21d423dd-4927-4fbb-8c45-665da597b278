<template>
  <div>
    <introSection
      ref="intro"
      :readOnlyMode="readOnlyMode"
      :settings="settings.introSettings"
      v-model="updateSettings.introSettings"
      :isUpdatingProcessed="sections.intro.isUpdatingProcessed"
      :isDisabled="sections.intro.isDisabled"
      accountLevel
      @saveChanges="updateIntroSettings"
    />
    <vehicleHistorySection
      ref="vehicleHistory"
      :readOnlyMode="readOnlyMode"
      :settings="settings.vehicleHistorySettings"
      v-model="updateSettings.vehicleHistorySettings"
      :isUpdatingProcessed="sections.vehicleHistory.isUpdatingProcessed"
      :isDisabled="sections.vehicleHistory.isDisabled"
      accountLevel
      @saveChanges="updateVehicleHistorySettings"/>
    <vehicleMileageSection
      ref="vehicleMileage"
      :readOnlyMode="readOnlyMode"
      :settings="settings.mileageSettings"
      v-model="updateSettings.mileageSettings"
      :isUpdatingProcessed="sections.vehicleMileage.isUpdatingProcessed"
      :isDisabled="sections.vehicleMileage.isDisabled"
      accountLevel
      @saveChanges="updateVehicleMileageSettings"/>
    <warrantySection
      ref="warranty"
      :readOnlyMode="readOnlyMode"
      :settings="settings.warrantySettings"
      v-model="updateSettings.warrantySettings"
      :isUpdatingProcessed="sections.warranty.isUpdatingProcessed"
      :isDisabled="sections.warranty.isDisabled"
      accountLevel
      @saveChanges="updateWarrantySettings"/>
    <economySection
      ref="economy"
      :readOnlyMode="readOnlyMode"
      :settings="settings.economySettings"
      v-model="updateSettings.economySettings"
      :isUpdatingProcessed="sections.economy.isUpdatingProcessed"
      :isDisabled="sections.economy.isDisabled"
      accountLevel
      @saveChanges="updateEconomySettings"/>
    <engineSection
      ref="engine"
      :readOnlyMode="readOnlyMode"
      :settings="settings.engineSettings"
      v-model="updateSettings.engineSettings"
      :isUpdatingProcessed="sections.engine.isUpdatingProcessed"
      :isDisabled="sections.engine.isDisabled"
      accountLevel
      @saveChanges="updateEngineSettings"/>
    <fuelTypesSection
      ref="fuelTypes"
      :readOnlyMode="readOnlyMode"
      :settings="settings.fuelTypeSettings"
      v-model="updateSettings.fuelTypeSettings"
      :isUpdatingProcessed="sections.fuelTypes.isUpdatingProcessed"
      :isDisabled="sections.fuelTypes.isDisabled"
      accountLevel
      @saveChanges="updateFuelTypesSettings"/>
    <featuresSection
      ref="features"
      :readOnlyMode="readOnlyMode"
      :settings="settings.featureSettings"
      v-model="updateSettings.featureSettings"
      :isUpdatingProcessed="sections.features.isUpdatingProcessed"
      :isDisabled="sections.features.isDisabled"
      accountLevel
      @saveChanges="updateFeaturesSettings"/>
    <outroSection
      ref="outro"
      :readOnlyMode="readOnlyMode"
      :settings="settings.outroSettings"
      v-model="updateSettings.outroSettings"
      :isUpdatingProcessed="sections.outro.isUpdatingProcessed"
      :isDisabled="sections.outro.isDisabled"
      accountLevel
      @saveChanges="updateOutroSettings"/>
    <summarySection
      ref="summary"
      :readOnlyMode="readOnlyMode"
      :settings="settings.descriptionTextSections"
      v-model="updateSettings.descriptionTextSections"
      :isUpdatingProcessed="sections.summary.isUpdatingProcessed"
      :isDisabled="sections.summary.isDisabled"
      accountLevel
      @saveChanges="updateDescriptionTextSettings"
    />
    <detail-row :extra-large-payload-width="true" :title-position="'start'">
      <span slot="title">Final Example:</span>
      <span slot="payload">{{ exampleText }}</span>
    </detail-row>
  </div>
</template>

<script>
import introSection from './ttsSettingsSections/introSection.vue'
import vehicleHistorySection from './ttsSettingsSections/vehicleHistorySection.vue'
import vehicleMileageSection from './ttsSettingsSections/vehicleMileageSection.vue'
import economySection from './ttsSettingsSections/economySection.vue'
import engineSection from './ttsSettingsSections/engineSection.vue'
import featuresSection from './ttsSettingsSections/featuresSection.vue'
import fuelTypesSection from './ttsSettingsSections/fuelTypesSection.vue'
import outroSection from './ttsSettingsSections/outroSection.vue'
import warrantySection from './ttsSettingsSections/warrantySection.vue'
import summarySection from './ttsSettingsSections/summarySection.vue'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '../../../globals'
import videoEncoderTypes from '@/shared/inventory/videoEncoderTypes'
import VideoEncoderService from '../../../services/inventory/VideoEncoderService'

const descriptionTextSections = videoEncoderTypes.descriptionTextSections

export default {
  name: 'tts-settings',
  props: {
    settings: {
      type: Object,
      required: true
    },
    readOnlyMode: {
      type: Boolean
    }
  },
  data () {
    return {
      updateSettings: globals().getClonedValue(this.settings),
      accountId: +this.$route.params.accountId,
      exampleText: '',
      sections: {
        intro: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        vehicleHistory: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        vehicleMileage: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        warranty: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        economy: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        engine: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        fuelTypes: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        features: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        outro: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true},
        summary: {isDisabled: false, isUpdatingProcessed: false, isViewMode: true}
      }
    }
  },
  created () {
    this.generateExampleText()
  },
  components: {
    introSection,
    vehicleHistorySection,
    vehicleMileageSection,
    economySection,
    engineSection,
    featuresSection,
    fuelTypesSection,
    outroSection,
    warrantySection,
    summarySection,
    detailRow
  },
  methods: {
    saveChanges (sectionKey) {
      VideoEncoderService.updateAccountPhotoToVideoDescriptionSettings(this.accountId, this.updateSettings).then(res => {
        this.$toaster.success('Updated TTS Settings Successfully')
        this.$refs[sectionKey].changeMode(true)
        this.$emit('refresh')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Failed on Updating TTS Settings', {timeout: 5000})
      }).finally(() => {
        this.setSectionsToDefault()
      })
    },
    updateIntroSettings (introSettings) {
      this.sections.intro.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('intro')
      this.$set(this.updateSettings, 'introSettings', introSettings)
      this.saveChanges('intro')
    },
    updateVehicleHistorySettings (vehicleHistorySettings) {
      this.sections.vehicleHistory.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('vehicleHistory')
      this.$set(this.updateSettings, 'vehicleHistorySettings', vehicleHistorySettings)
      this.saveChanges('vehicleHistory')
    },
    updateVehicleMileageSettings (mileageSettings) {
      this.sections.vehicleMileage.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('vehicleMileage')
      this.$set(this.updateSettings, 'mileageSettings', mileageSettings)
      this.saveChanges('vehicleMileage')
    },
    updateWarrantySettings (warrantySettings) {
      this.sections.warranty.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('warranty')
      this.$set(this.updateSettings, 'warrantySettings', warrantySettings)
      this.saveChanges('warranty')
    },
    updateEconomySettings (economySettings) {
      this.sections.economy.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('economy')
      this.$set(this.updateSettings, 'economySettings', economySettings)
      this.saveChanges('economy')
    },
    updateDescriptionTextSettings (descriptionTextSections) {
      this.sections.summary.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('summary')
      this.$set(this.updateSettings, 'descriptionTextSections', descriptionTextSections)
      this.saveChanges('summary')
    },
    updateEngineSettings (engineSettings) {
      this.sections.engine.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('engine')
      this.$set(this.updateSettings, 'engineSettings', engineSettings)
      this.saveChanges('engine')
    },
    updateOutroSettings (outroSettings) {
      this.sections.outro.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('outro')
      this.$set(this.updateSettings, 'outroSettings', outroSettings)
      this.saveChanges('outro')
    },
    updateFeaturesSettings (featureSettings) {
      this.sections.features.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('features')
      this.$set(this.updateSettings, 'featureSettings', featureSettings)
      this.saveChanges('features')
    },
    updateFuelTypesSettings (fuelTypeSettings) {
      this.sections.fuelTypes.isUpdatingProcessed = true
      this.makeOtherSectionsDisabled('fuelTypes')
      this.$set(this.updateSettings, 'fuelTypeSettings', fuelTypeSettings)
      this.saveChanges('fuelTypes')
    },
    makeOtherSectionsDisabled (excludeSectionKey) {
      Object.keys(this.sections).filter(key => key !== excludeSectionKey).forEach(key => {
        this.sections[key].isDisabled = true
      })
    },
    setSectionsToDefault () {
      Object.values(this.sections).forEach(section => {
        section.isDisabled = false
        section.isUpdatingProcessed = false
      })
    },
    generateExampleText () {
      let exampleText = this.getIntroText()
      exampleText = exampleText + ' ' + '2019 Audi A4'
      let excludeSections = [descriptionTextSections.intro.value, descriptionTextSections.outro.value]
      let sections = this.updateSettings.descriptionTextSections.orderedSectionItems.filter(x => x.isEnabled && !excludeSections.includes(x.section)).map(x => x.section)
      for (let section of sections) {
        switch (section) {
          case descriptionTextSections.vehicleHistory.value:
            exampleText = this.appendText(exampleText, this.getVehicleHistoryText())
            break
          case descriptionTextSections.mileage.value:
            exampleText = this.appendText(exampleText, this.getVehicleMileageText())
            break
          case descriptionTextSections.warranty.value:
            exampleText = this.appendText(exampleText, this.getWarrantyText())
            break
          case descriptionTextSections.fuelEconomy.value:
            exampleText = this.appendText(exampleText, this.getEconomyText())
            break
          case descriptionTextSections.engine.value:
            exampleText = this.appendText(exampleText, this.getEngineText())
            break
          case descriptionTextSections.fuelType.value:
            exampleText = this.appendText(exampleText, this.getFuelTypeText())
            break
          case descriptionTextSections.features.value:
            exampleText = this.appendText(exampleText, this.getFeaturesText())
            break
        }
      }
      exampleText = this.appendText(exampleText, this.getOutroText())
      this.exampleText = exampleText.trim()
    },
    appendText (text, append) {
      if (append) {
        text = text + '. ' + append
      }
      return text
    },
    getIntroText () {
      return (this.updateSettings.introSettings.introItems.findLast(x => x.isEnabled) || { introText: '' }).introText
    },
    getVehicleHistoryText () {
      return (this.updateSettings.vehicleHistorySettings.carfaxOneOwnerDescriptionText || '').replaceAll('{VehicleModel}', 'A4')
    },
    getVehicleMileageText () {
      let text = this.updateSettings.mileageSettings.mileageTextTemplate || ''
      let replaceOptions = [{ from: '{MileageDescription}', to: '40k miles' }, { from: '{VehicleModel}', to: 'A4' }]
      replaceOptions.forEach(x => {
        text = text.replaceAll(x.from, x.to)
      })
      return text
    },
    getWarrantyText () {
      let warranties = Object.values(this.updateSettings.warrantySettings.warrantyItems)
      let randomIndex = Math.floor(Math.random() * warranties.length)
      return warranties[randomIndex].descriptionText
    },
    getEconomyText () {
      let text = this.updateSettings.economySettings.fuelEconomyTextTemplate || ''
      let replaceOptions = [
        { from: '{CityMileage}', to: '15' },
        { from: '{HighwayMileage}', to: '8' },
        { from: '{RunUnits}', to: 'miles' }
      ]
      replaceOptions.forEach(x => {
        text = text.replaceAll(x.from, x.to)
      })
      return text
    },
    getEngineText () {
      let engineText = this.updateSettings.engineSettings.engineDescriptionTextTemplate || ''
      engineText = engineText.replaceAll('{EngineDescription}', '3.5L Engine').replaceAll('{VehicleModel}', 'A4')
      let transmissionText = this.updateSettings.engineSettings.transmissionDescriptionTextTemplate || ''
      transmissionText = transmissionText.replaceAll('{TransmissionDescription}', 'Automatic Transmission')
      return engineText + ', ' + transmissionText
    },
    getFuelTypeText () {
      let fuelTypes = Object.values(this.updateSettings.fuelTypeSettings.fuelTypeItems)
      let randomIndex = Math.floor(Math.random() * fuelTypes.length)
      return fuelTypes[randomIndex].descriptionText
    },
    getFeaturesText () {
      let text = this.updateSettings.featureSettings.featuresIntro || ''
      let enabledFeatures = this.updateSettings.featureSettings.featureItems.filter(x => x.isEnabled)
      let featuresText = []
      for (let index = 0; index < Math.min(this.updateSettings.featureSettings.keyFeaturesLimitInDescription, enabledFeatures.length); index++) {
        if (enabledFeatures[index].featureCustomText) {
          featuresText.push(enabledFeatures[index].featureCustomText)
        } else {
          featuresText.push(enabledFeatures[index].featureName)
        }
      }
      return text + ' ' + featuresText.join(', ')
    },
    getOutroText () {
      return (this.updateSettings.outroSettings.outroItems.findLast(x => x.isEnabled) || { outroText: '' }).outroText
    }
  },
  watch: {
    'updateSettings': {
      deep: true,
      handler: function () {
        this.generateExampleText()
      }
    },
    'settings': {
      deep: true,
      handler: function () {
        this.updateSettings = globals().getClonedValue(this.settings)
      }
    }
  }
}
</script>
