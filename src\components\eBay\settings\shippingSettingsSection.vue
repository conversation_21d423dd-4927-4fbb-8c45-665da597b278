<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Shipping Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Shipping Instructions:</span>
        <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.EBayShippingStatement" slot="payload" :options="getShippingInstructionOptions"></b-form-select>
        <span slot="payload" v-else>{{getShippingInstructionDesc}}</span>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Ship to Locations:</span>
        <div slot="payload" class="w-100">
          <span class="custom-text-nowrap"><strong>In addition to the United States </strong>I will arrange shipping to these countries:</span>
          <div class="my-2" v-if="!isViewMode">
            <b-form-checkbox-group
              v-model="settingsToUpdate.ShipToLocations"
              stacked
            >
            <b-row>
              <b-col cols="6" v-for="option in getShippingOptions" :key="option.value">
                <b-form-checkbox :value="option.value">
                  {{option.text}}
                </b-form-checkbox>
              </b-col>
            </b-row>
            </b-form-checkbox-group>
          </div>
          <span v-else>{{getShippingLocationsDesc}}</span>
          <span class="custom-text-nowrap">Select shipping location(s) to display in your eBay Motors listings.</span>
        </div>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'
import globals from '../../../globals'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  computed: {
    getShippingInstructionOptions () {
      return [{value: 0, text: 'Buyer responsible for pick-up or shipping'}, {value: 1, text: 'See item description for shipping details'}]
    },
    getShippingInstructionDesc () {
      let res = this.getShippingInstructionOptions.find(x => x.value === this.settings.EBayShippingStatement)
      if (res) {
        return res.text
      }

      return 'Undefined'
    },
    getShippingOptions () {
      return [
        {value: 'Americas', text: 'Americas'},
        {value: 'AU', text: 'Australia'},
        {value: 'Asia', text: 'Asia'},
        {value: 'CA', text: 'Canada'},
        {value: 'DE', text: 'Germany'},
        {value: 'Europe', text: 'Europe'},
        {value: 'JP', text: 'Japan'},
        {value: 'MX', text: 'Mexico'},
        {value: 'GB', text: 'United Kingdom'},
        {value: 'Worldwide', text: 'Worldwide'}
      ]
    },
    getShippingLocationsDesc () {
      let locations = (this.settings.ShipToLocations || [])
      let res = []
      locations.map(x => {
        let location = this.getShippingOptions.find(option => option.value === x)
        if (location) {
          res.push(location.text)
        }
      })
      return res.join(', ')
    }
  },
  mixins: [editSettingsMixin],
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateShippingSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Shipping Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay account shipping setting')
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 769px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
</style>
