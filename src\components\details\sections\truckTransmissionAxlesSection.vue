<template>
  <details-section title="Transmission / Axles" @cancel="onCancel" v-model="mode" v-if="truckAttributes">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row title="Drivetrain" :text="getDrivetrainDescription"/>

      <auto-detail-row :title="truckFeatures.driveAxles.name" :text="getSelectedAttributeOption(truckFeatures.driveAxles).key"/>

      <auto-detail-row :title="getAxelMakerRatioInfo.title" :text="getAxelMakerRatioInfo.text"/>

      <auto-detail-row :title="truckFeatures.suspensionType.name" :text="getSelectedAttributeOption(truckFeatures.suspensionType).key"/>

      <auto-detail-row title="Transmission" :text="[getTransGearsDescription, getTransmissionDescription]"/>

      <auto-detail-row :title="truckFeatures.transmissionMaker.name" :text="getSelectedAttributeOption(truckFeatures.transmissionMaker).key"/>

      <auto-detail-row :title="truckFeatures.transmissionModel.name" :text="truckFeatures.transmissionModel.value"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row title="Drivetrain" v-model="vehicle.drivetrainId" :options="metadata.drivetrainOptions" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.driveAxles.name" v-model="truckFeatures.driveAxles.value" :options="getNameValueOptions(truckFeatures.driveAxles.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.axleMaker.name" v-model="truckFeatures.axleMaker.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.ratio.name" v-model="truckFeatures.ratio.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.suspensionType.name" v-model="truckFeatures.suspensionType.value" :options="getNameValueOptions(truckFeatures.suspensionType.nameValueOptions)" />

      <detail-row fixedPayloadWidth editMode>
        <span slot="title">Transmission:</span>
        <div slot="payload" class="d-flex" style="flex-grow: 1">
          <b-form-select v-model="vehicle.transGearsId" :options="metadata.transmissionGearsOptions"></b-form-select>
          <span class="px-2"></span>
          <b-form-select v-model="vehicle.transTypeId" :options="metadata.transmissionOptions"></b-form-select>
        </div>
      </detail-row>

      <auto-detail-row :title="truckFeatures.transmissionMaker.name" v-model="truckFeatures.transmissionMaker.value" :options="getNameValueOptions(truckFeatures.transmissionMaker.nameValueOptions)" />

      <auto-detail-row :title="truckFeatures.transmissionModel.name" v-model="truckFeatures.transmissionModel.value" validation-rule="xml"/>

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'
import detailRow from '../helpers/detailRow'
import featuresHelper from '../../../shared/details/featuresHelper'

export default {
  name: 'truck-trans-axles-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata', 'truckAttributes']),
    truckFeatures () {
      return {
        driveAxles: this.getFeatureById(-10097),
        axleMaker: this.getFeatureById(-10098),
        ratio: this.getFeatureById(-10099),
        suspensionType: this.getFeatureById(-10100),
        transmissionMaker: this.getFeatureById(-10095),
        transmissionModel: this.getFeatureById(-10096)
      }
    },
    getAxelMakerRatioInfo () {
      return {
        title: `${this.truckFeatures.axleMaker.name} / ${this.truckFeatures.ratio.name}`,
        text: `${this.truckFeatures.axleMaker.value} / ${this.truckFeatures.ratio.value}`
      }
    },
    getDrivetrainDescription () {
      return this.metadata.drivetrainOptions[this.vehicle.drivetrainId] || ''
    },
    getTransmissionDescription () {
      return this.metadata.transmissionOptions[this.vehicle.transTypeId] || ''
    },
    getTransGearsDescription () {
      return this.metadata.transmissionGearsOptions[this.vehicle.transGearsId] || ''
    }
  },
  methods: {
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.truckAttributes, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'auto-detail-row': autoDetailRow,
    'detail-row': detailRow
  }
}
</script>
