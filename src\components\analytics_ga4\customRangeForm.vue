<template>
  <b-form>
    <b-row class="mb-3 align-items-center">
      <b-col sm="6" class="mb-3 mb-sm-0">
        <b-input-group class="flex-nowrap">
          <datepicker
            class="popup-top"
            v-model="dateFrom"
            format="MM/dd/yyyy"
            placeholder="from"
            :bootstrap-styling="true"
            :disabledDates="dateFromDisabledDates"
          />
          <b-input-group-append is-text>
            <i class="ion ion-md-calendar" slot="append"></i>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col sm="6">
        <b-input-group class="flex-nowrap">
          <datepicker
             class="popup-top"
            v-model="dateTo"
            format="MM/dd/yyyy"
            placeholder="to"
            :bootstrap-styling="true"
            :disabledDates="dateToDisabledDates"
          />
          <b-input-group-append is-text>
            <i class="ion ion-md-calendar" slot="append"></i>
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-row v-if="enableCustomRangeDisclaimer" class="mb-3 text-right">
      <b-col cols="12">
        <span class="disclaimer">*Custom Range works within scope of a month.</span>
      </b-col>
    </b-row>
    <b-row>
      <b-col>
        <b-button :disabled="disabled" variant="primary btn-round" class="ml-auto d-block" @click="notifySubmitClicked">
          {{ actionText }}
        </b-button>
      </b-col>
    </b-row>
  </b-form>
</template>

<script>
import Datepicker from 'vuejs-datepicker'
import analyticsConstants from '@/shared/analytics/constants'

const componentDefaults = {
  minDate: analyticsConstants.beginOfTime.toDate()
}

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    actionText: {
      type: String,
      default: 'Apply'
    },
    enableCustomRangeDisclaimer: {
      type: Boolean,
      default: true
    }
  },
  components: {
    'datepicker': Datepicker
  },
  data () {
    return {
      dateFrom: new Date(),
      dateTo: new Date()
    }
  },
  computed: {
    dateFromDisabledDates () {
      return {
        to: componentDefaults.minDate,
        from: new Date(),
        customPredictor: (date) => {
          return date.getTime() > this.dateTo.getTime()
        }
      }
    },
    dateToDisabledDates () {
      return {
        to: componentDefaults.minDate,
        from: new Date(),
        customPredictor: (date) => {
          return date.getTime() < this.dateFrom.getTime()
        }
      }
    }
  },
  methods: {
    notifySubmitClicked () {
      this.$emit('submitted', {
        dateFrom: this.dateFrom,
        dateTo: this.dateTo
      })
    }
  }
}
</script>

<style scoped>
.disclaimer {
  font-size: 10px;
}
.vdp-datepicker {
  flex-grow: 1;
}
</style>

<style>
.popup-top .vdp-datepicker__calendar {
  bottom: 100%;
}
</style>
