<template>
  <div v-if="totalItems > 0">
    <b-card no-body>
      <b-table
        class="products-table card-table"
        :items="tableItems"
        :sort-by="tableSortBy"
        :sort-desc="tableSortDesc"
        @sort-changed="onSortChanged"
        :fields="tableFields"
        :striped="true"
        :bordered="false"
        :no-sort-reset="true"
        :no-local-sorting="true"
        responsive
      >
        <template #cell(title)="row">
          <div class="media align-items-center">
            <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="getVehiclePhotoSrc(row.item)">
            <span>{{row.item | getVehicleTitle}}</span>
          </div>
        </template>
        <template #cell(details)="row">
          <div class="media align-items-center">
            <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
              {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
            </b-button>
          </div>
        </template>
        <template #row-details="row">
          <b-card>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>Stock#:</b></b-col>
              <b-col>{{ row.item.StockNumber }}</b-col>
            </b-row>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>VIN:</b></b-col>
              <b-col>{{ row.item.Vin }}</b-col>
            </b-row>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>Miles:</b></b-col>
              <b-col>{{ row.item.Miles || '-' }}</b-col>
            </b-row>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>Engine:</b></b-col>
              <b-col>{{ row.item.EngineDescription || '-' }}</b-col>
            </b-row>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>Transmission:</b></b-col>
              <b-col>{{ row.item.TransmissionDescription || '-' }}</b-col>
            </b-row>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>Ext. Color:</b></b-col>
              <b-col>{{ row.item.ExtColorDescription || '-' }}</b-col>
            </b-row>
            <b-row class="mb-2">
              <b-col sm="12" md="4" lg="3" xl="2"><b>Int. Color:</b></b-col>
              <b-col>{{ row.item.IntColorDescription || '-' }}</b-col>
            </b-row>
            <b-button size="sm" @click="row.toggleDetails">Hide Details</b-button>
          </b-card>
        </template>
        <template #cell(currentStatus)="row">
          {{ row.item | getEBayPostStatus }}
        </template>
        <template #cell(actions)="row">
          <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
            <template slot="button-content">
              <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
            </template>
            <b-dropdown-item :to="{ path: `/inventory/${row.item.AccountId}/edit/${row.item.Vin}` }">Edit Vehicle</b-dropdown-item>
            <b-dropdown-item v-if="isSendToEBayEnabled(row.item)"
              :to="{ path: `/ebay/${row.item.AccountId}/inventory/${row.item.Vin}/schedule` }"
            >
              Send to eBay
            </b-dropdown-item>
            <b-dropdown-item v-if="isReviseEnabled(row.item)"
              :to="{ path: `/ebay/${row.item.AccountId}/posts/${row.item.AuctionId}/revise` }"
            >
              Revise eBay
            </b-dropdown-item>
            <b-dropdown-item v-if="ebayVehicleStatus.active.value === row.item.EBayVehicleStatus"
              @click="showEndAuctionModal(row.item)"
            >
              End eBay Listing
            </b-dropdown-item>
            <b-dropdown-item v-if="ebayVehicleStatus.scheduled.value === row.item.EBayVehicleStatus"
              :to="{ path: `/ebay/${row.item.AccountId}/inventory/${row.item.Vin}/schedule` }"
            >
              Unschedule
            </b-dropdown-item>
            <b-dropdown-item v-if="isRelistEnabled(row.item)"
              :to="{ path: `/ebay/${row.item.AccountId}/inventory/${row.item.Vin}/schedule` }"
            >
              Relist
            </b-dropdown-item>
            <b-dropdown-item v-if="ebayVehicleStatus.neverPosted.value !== row.item.EBayVehicleStatus"
              :to="{ path: `/ebay/${row.item.AccountId}/posts/${row.item.Vin}/history` }"
            >
              eBay History
            </b-dropdown-item>
            <b-dropdown-item :href="getViewTemplateHref(row.item)">View Template</b-dropdown-item>
          </b-dropdown>
        </template>
      </b-table>
      <!-- / Pagination -->
      <paging
        :pageNumber="pageNumber"
        :pageSize="pageSize"
        :totalItems="totalItems"
        titled
        pageSizeSelector
        @numberChanged="onPageNumberChanged"
        @changePageSize="onPageSizeChanged"
      />
    </b-card>
    <endAuctionModal
      v-if="endAuctionItem"
      :isVisible="isVisibleEndAuctionModal"
      :auctionId="endAuctionItem.AuctionId"
      :accountId="endAuctionItem.AccountId"
      @hide="hideEndAuctionModal"
    />
  </div>
  <div v-else>
    No vehicles found
  </div>
</template>

<script>
import numeral from 'numeral'
import paging from './../../../components/_shared/paging.vue'
import eBayConstants from './../../../shared/ebay/constants'
import inventoryFiltersMixin from './../../../mixins/eBay/inventoryFiltersMixin'
import moment from 'moment'
import endAuctionModal from '@/components/eBay/posts/endAuctionModal'
import vehicleStatuses from '@/shared/common/vehicle/vehicleStatuses'

export default {
  name: 'inventory-listing-table',
  components: {
    'paging': paging,
    endAuctionModal
  },
  mixins: [inventoryFiltersMixin],
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true },
    isPostingAllowed: { type: Boolean, required: true },
    status: { type: Number, required: true },
    sitesInfo: { type: Object, required: true }
  },
  data () {
    return {
      isVisibleEndAuctionModal: false,
      endAuctionItem: null,
      ebayVehicleStatus: eBayConstants.eBayInventoryVehicleStatus,
      tableFields: [{
        key: 'title',
        label: 'Vehicle',
        sortable: false,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'details',
        label: '',
        sortable: false,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'StockNumber',
        label: 'Stock #',
        sortable: true,
        sortTypeAsc: eBayConstants.inventorySortTypes.stockNumberAsc,
        sortTypeDesc: eBayConstants.inventorySortTypes.stockNumberDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'DaysInStock',
        label: 'Days in Stock',
        sortable: true,
        sortTypeAsc: eBayConstants.inventorySortTypes.daysInStockAsc,
        sortTypeDesc: eBayConstants.inventorySortTypes.daysInStockDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'Price',
        label: 'Vehicle Price',
        sortable: true,
        sortTypeAsc: eBayConstants.inventorySortTypes.priceAsc,
        sortTypeDesc: eBayConstants.inventorySortTypes.priceDesc,
        tdClass: 'py-2 align-middle',
        formatter: value => numeral(value).format('$0,0')
      }, {
        key: 'ActualPhotosCount',
        label: 'Number of Photos',
        sortable: true,
        sortTypeAsc: eBayConstants.inventorySortTypes.photosAsc,
        sortTypeDesc: eBayConstants.inventorySortTypes.photosDesc,
        tdClass: 'py-2 align-middle'
      }, {
        key: 'currentStatus',
        tdClass: 'py-2 align-middle'
      }, {
        key: 'actions',
        tdClass: 'py-2 align-middle'
      }]
    }
  },
  filters: {
    getVehicleTitle: function (item) {
      if (!item) return ''
      let title = ''
      if (item.year > 0) {
        title = item.Year.toString()
      }

      title = [title, item.Make, item.Model, item.Trim].filter(v => v && v !== '').join(' ')

      return title.trim()
    },
    getEBayPostStatus: function (item) {
      let res = Object.values(eBayConstants.eBayInventoryVehicleStatus).find(x => x.value === item.EBayVehicleStatus)
      return (res || { text: 'Undefined' }).text
    }
  },
  computed: {
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)

      this.$emit('update:sortType', sortingColumn
        ? value.sortDesc
          ? sortingColumn.sortTypeDesc
          : sortingColumn.sortTypeAsc
        : 0)
    },
    isRelistEnabled (item) {
      return this.isVehicleHasStatusLive(item) &&
        this.ebayVehicleStatus.ended.value === item.EBayVehicleStatus &&
        moment().diff(item.AuctionEndDateTime, 'days') < 90
    },
    isSendToEBayEnabled (item) {
      return this.isVehicleHasStatusLive(item) &&
        this.ebayVehicleStatus.active.value !== item.EBayVehicleStatus &&
        !this.isRelistEnabled(item) &&
        this.ebayVehicleStatus.scheduled.value !== item.EBayVehicleStatus
    },
    isVehicleHasStatusLive (item) {
      return item.VehicleStatus === vehicleStatuses.live.value
    },
    isReviseEnabled (item) {
      return this.ebayVehicleStatus.active.value === item.EBayVehicleStatus
    },
    getVehiclePhotoSrc (item) {
      return item.ImageUrl || '#'
    },
    getViewTemplateHref (item) {
      let res = this.sitesInfo[item.AccountId]
      if (res) {
        return `${res.host}/auction.aspx?vin=${item.Vin}`
      }
      return ''
    },
    showEndAuctionModal (item) {
      this.endAuctionItem = item
      this.isVisibleEndAuctionModal = true
    },
    hideEndAuctionModal () {
      this.endAuctionItem = null
      this.isVisibleEndAuctionModal = false
    }
  }
}
</script>
