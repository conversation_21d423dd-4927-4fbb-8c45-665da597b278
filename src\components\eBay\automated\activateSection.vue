<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Activate Automated eBay" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Launch Settings:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasToAutoLaunch" :options="getLaunchSettingOptions"></b-form-select>
          <span v-else>{{getLaunchDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Auto-Launch means the eBizAutos System automatically launches your listings - includes eBay Local Auto-Sync.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Launch Vehicle Types:</span>
        <b-form-checkbox-group v-if="!isViewMode" slot="payload" v-model="settingsToUpdate.VehicleConditions" :options="checkboxOptions"></b-form-checkbox-group>
        <span v-else slot="payload">{{getVehicleConditionsDesc}}</span>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import globals from '@/globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import ebayOptions from '@/shared/ebay/ebayOptions'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings),
      checkboxOptions: ebayOptions.vehicleConditionOptions
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  computed: {
    getLaunchSettingOptions () {
      return [
        {
          value: false,
          text: 'Manual'
        },
        {
          value: true,
          text: 'Auto Launch'
        }
      ]
    },
    getLaunchDesc () {
      return (this.getLaunchSettingOptions.find(x => x.value === this.settings.HasToAutoLaunch) || {}).text || ''
    },
    getVehicleConditionsDesc () {
      return ((ebayOptions.vehicleConditionOptions.filter(x => this.settings.VehicleConditions.includes(x.value)) || []).map(x => x.text) || []).join(', ') || '-'
    },
    getVehicleConditionOptions () {
      return ebayOptions.vehicleConditionOptions
    }
  },
  methods: {
    updateSettings () {
      if (!this.settingsToUpdate.VehicleConditions || this.settingsToUpdate.VehicleConditions.length === 0) {
        this.settingsToUpdate.HasToAutoLaunch = false
      }

      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateAutomatedActivatedSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Activated Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, 'Cannot update eBay automated activated settings', this.settingsToUpdate)
        }
      }).finally(() => {
        this.isViewMode = true
        this.isUpdatingProcessed = false
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
</style>
