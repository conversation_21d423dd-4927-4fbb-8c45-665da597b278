<template>
  <b-form @submit.prevent="onSubmit" class='pl-3 pr-3'>
    <div class="form-row">
      <b-col sm="2 pl-2 pt-4 pr-2 pb-4">
        <b-form-input
          max='200'
          v-model="filter.search"
          placeholder="Search Stock# or ID"
          autocomplete="off"
        >
        </b-form-input>
      </b-col>
      <b-col sm="2 pl-2 pt-4 pr-2 pb-4">
        <b-form-input
          max='200'
          v-model="filter.area"
          placeholder="Search Area"
          autocomplete="off"
        >
        </b-form-input>
      </b-col>

      <b-col sm="2 pl-2 pt-4 pr-2 pb-4">
        <b-input-group class="flex-nowrap">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeFrom"
            v-model="filter.dateFrom"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Post Date From"
            className="form-control"
            @change="onTimeFromInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateFrom"
            @click="filter.dateFrom = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </b-col>

      <b-col sm="2 pl-2 pt-4 pr-2 pb-4">
        <b-input-group class="flex-nowrap">
          <b-input-group-prepend is-text>
            <i class="ion ion-md-calendar" slot="prepend"></i>
          </b-input-group-prepend>
          <date-time-picker
            ref="timeTo"
            v-model="filter.dateTo"
            :options="filterTimeOptions"
            format="MM/DD/YYYY HH:mm"
            placeholder="Post Date To"
            className="form-control"
            @change="onTimeToInputChange"
          />
          <b-input-group-append
            is-text
            v-show="filter.dateTo"
            @click="filter.dateTo = null"
          >
            <i class="ion ion-md-close"></i>
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col sm="4 pl-2 pt-4 pr-2 pb-4">
        <b-btn block  variant="primary" type="submit">Submit</b-btn>
      </b-col>
    </div>
  </b-form>
</template>

<script>

export default {
  name: 'posts-filters',
  props: {
    filter: { type: Object, required: true }
  },
  data () {
    return {
      filterTimeOptions: {
        autoUpdateInput: false,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: new Date()
      }
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker')
  },
  computed: {
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    }
  },
  methods: {
    onSubmit () {
      this.$emit('filterChanged', this.filter)
    },
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filter.dateFrom || null
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filter.dateTo || null
    }
  }
}
</script>
