<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="eBay Login & Authorization" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row :large-payload-width="true">
        <span slot="title">Authorization Status:</span>
        <span slot="payload" v-if="eBayUserInfo.AuthTokenExpirationDate">
          <span v-if="eBayUserInfo.IsTokenExpired">
            <i class="ion ion-ios-close h4 m-0 mr-1 opacity-75"></i>
            No Current eBay Authorization, Token Expired - {{getDate(eBayUserInfo.AuthTokenExpirationDate)}}
          </span>
          <span v-else>
            <i class="ion ion-ios-checkmark h4 m-0 mr-1 opacity-75"></i>
            <strong>Authorized</strong> - Current Authorization expires {{getDate(eBayUserInfo.AuthTokenExpirationDate)}}
          </span>
        </span>
        <span slot="payload" v-else>
          <i class="ion ion-ios-close h4 m-0 mr-1 opacity-75"></i>
          No Current eBay Authorization
        </span>
      </detail-row>
      <detail-row v-if="!isViewMode" :large-payload-width="true">
        <span slot="title"></span>
        <l-button :loading="isUpdateAuthLoading" slot="payload" @click="onUpdateAuth" variant="primary">Update Authorization</l-button>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">eBay Username:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-input name="ebay-user-name" v-if="!isViewMode" v-model="ebayUserName" type="text" autocomplete="off"></b-form-input>
          <span v-else>{{eBayUserInfo.EBayUserName || '-'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Your eBay username and password are required to enable the "Get Bidder Contact info." feature.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">eBay Password:</span>
        <span slot="payload" v-if="eBayUserInfo.IsPasswordSaved"><i class="ion ion-ios-checkmark h4 m-0 mr-1 opacity-75"></i> Password Saved</span>
        <span slot="payload" v-else><i class="ion ion-ios-close h4 m-0 mr-1 opacity-75"></i>Password Not Saved</span>
      </detail-row>
      <ValidationProvider rules="password:@confirm" v-slot="{errors}">
      <detail-row v-if="!isViewMode" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Change eBay Password:</span>
        <b-form-input autocomplete="new-password" @input="onInputEbayPassword" @keydown.native="handleKeyDown" v-model="changedEBayPassword" name="password" slot="payload" type="password"></b-form-input>
      </detail-row>
      </ValidationProvider>
      <ValidationProvider name="confirm" v-slot="{errors}">
      <detail-row
        v-if="!isViewMode"
        title-position="start"
        :large-payload-width="true"
        :error="errors[0]"
      >
        <span slot="title">Confirm eBay Password:</span>
        <b-form-input autocomplete="new-password" @keydown.native="handleKeyDown" v-model="confirmedEBayPassword" name="password_confirmation" slot="payload" type="password"></b-form-input>
      </detail-row>
      </ValidationProvider>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import moment from 'moment'
import globals from '../../../globals'

export default {
  props: {
    eBayUserInfo: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isUpdateAuthLoading: false,
      isViewMode: true,
      ebayUserName: globals().getClonedValue(this.eBayUserInfo.EBayUserName),
      changedEBayPassword: globals().getClonedValue(this.eBayUserInfo.Password),
      confirmedEBayPassword: globals().getClonedValue(this.eBayUserInfo.Password)
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  methods: {
    saveSettings () {
      if (this.eBayUserInfo.Password === this.changedEBayPassword && this.ebayUserName === this.eBayUserInfo.EBayUserName) {
        this.isViewMode = true
        return
      }
      this.updateEBayUserInfo()
    },
    cancel () {
      this.ebayUserName = globals().getClonedValue(this.eBayUserInfo.EBayUserName)
      this.changedEBayPassword = globals().getClonedValue(this.eBayUserInfo.Password)
      this.confirmedEBayPassword = globals().getClonedValue(this.eBayUserInfo.Password)
      this.isViewMode = true
    },
    onUpdateAuth () {
      this.isUpdateAuthLoading = true
      this.$store.dispatch('eBay/getUserEBaySignInUrl', {
        accountId: this.eBayUserInfo.AccountId,
        contactId: this.eBayUserInfo.ContactId,
        redirectionUrl: window.location.href
      }).then(res => {
        window.open(res.data)
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
      }).finally(() => {
        this.isUpdateAuthLoading = false
      })
    },
    getDate (date) {
      return moment(date).format('MM/DD/YYYY')
    },
    updateEBayUserInfo () {
      this.isUpdatingProcessed = true
      let apiData = {
        eBayUserName: this.ebayUserName,
        eBayUserPassword: this.changedEBayPassword
      }
      this.$store.dispatch('eBay/updateEBayUserInfo', { accountId: this.eBayUserInfo.AccountId, contactId: this.eBayUserInfo.ContactId, data: apiData }).then(res => {
        this.$toaster.success('eBay User Information Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay user info', this.eBayUserInfo)
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    },
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    onInputEbayPassword (value) {
      if (!value.trim()) {
        this.confirmedEBayPassword = ''
      }
    },
    handleKeyDown (e) {
      if (e.key && e.key.length === 1 && e.key === ' ') {
        e.preventDefault()
      }
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
</style>
