<template>
  <b-modal
    :visible="isShowModal"
    @hide="hide"
    :title="getTitle"
    no-close-on-backdrop
    size="lg"
  >
    <detail-row fixedPayloadWidth v-if="reportInfo.reportType === 0">
      <strong slot="title">Daily</strong>
      <b-form-checkbox slot="payload" v-model="dailySelected" disabled></b-form-checkbox>
    </detail-row>
    <detail-row v-if="reportInfo.reportRangeType !== constants.reportRangeTypes.month" fixedPayloadWidth>
      <span slot="title">Date range:</span>
      <b-input-group slot="payload" class="flex-nowrap">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          v-model="dateRange"
          :options="filterTimeOptions"
          format="MM/DD/YYYY"
          className="form-control"
        />
      </b-input-group>
    </detail-row>
    <detail-row fixedPayloadWidth v-if="reportInfo.reportType === 0">
      <strong slot="title">Monthly</strong>
      <b-form-checkbox slot="payload" v-model="monthlySelected"></b-form-checkbox>
    </detail-row>
    <detail-row bigPayloadWidth v-if="monthlySelected || reportInfo.reportRangeType === constants.reportRangeTypes.month">
      <span slot="title">Month Range:</span>
      <div slot="payload" class="d-flex flex-row">
        <b-form-select v-model="model.rebuildMonthFrom" :options="getRangeMonthFromOptions" @change="onRebuildMonthFromChange"></b-form-select>
        <b-form-select v-model="model.rebuildMonthTo" :options="rangeMonthToOptions" class="ml-2"></b-form-select>
      </div>
    </detail-row>
    <template #modal-footer>
      <b-btn @click="hide">Close</b-btn>
      <b-btn @click="rebuild" variant="primary">Rebuild</b-btn>
    </template>
  </b-modal>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/analytics/constants'
import moment from 'moment'
import globals from '../../../globals'

export default {
  name: 'analytics-brand-export-rebuild-modal',
  props: {
    reportInfo: { type: Object, required: true },
    isShowModal: { type: Boolean, required: true }
  },
  data () {
    return {
      dailySelected: true,
      monthlySelected: false,
      constants,
      model: {
        rebuildDateFrom: moment().format('MM/DD/YYYY'),
        rebuildDateTo: moment().format('MM/DD/YYYY'),
        rebuildMonthFrom: moment().add(-1, 'M').startOf('month').format('MM/DD/YYYY HH:mm'),
        rebuildMonthTo: moment().add(-1, 'M').endOf('month').format('MM/DD/YYYY HH:mm')
      },
      filterTimeOptions: {
        autoUpdateInput: true,
        maxDate: new Date()
      },
      date: '',
      rangeMonthToOptions: [{
        value: moment().add(-1, 'M').endOf('month').format('MM/DD/YYYY HH:mm'),
        text: moment().add(-1, 'M').endOf('month').format('MMMM, YYYY')
      }]
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker'),
    'detail-row': detailRow
  },
  computed: {
    getTitle () {
      if (this.reportInfo.reportType === 0) { // 0 undefined
        return `Rebuild and Resend Reports for ${this.reportInfo.exportName}`
      } else {
        let res = constants.reportTypes.find(x => x.value === this.reportInfo.reportType)
        return `Rebuild and Resend ${res ? res.text : ''} Report for ${this.reportInfo.exportName}`
      }
    },
    dateRange: {
      get () {
        return [this.model.rebuildDateFrom, this.model.rebuildDateTo]
      },
      set (newRangeDate) {
        this.model.rebuildDateFrom = newRangeDate[0] || this.model.rebuildDateFrom
        this.model.rebuildDateTo = newRangeDate[1] || this.model.rebuildDateTo
      }
    },
    getRangeMonthFromOptions () {
      let options = []
      let date = moment().add(-60, 'M').startOf('month')
      for (date; date < moment().startOf('month'); date.add(1, 'M')) {
        options.push(
          {
            value: date.startOf('month').format('MM/DD/YYYY HH:mm'),
            text: date.format('MMMM, YYYY')
          }
        )
      }

      return options.reverse()
    }
  },
  methods: {
    hide () {
      this.$emit('hide')
      this.setDefaultValue()
    },
    rebuild () {
      let apiModel = {
        exportId: this.reportInfo.exportId,
        reportType: this.reportInfo.reportType,
        reportRangeType: this.reportInfo.reportRangeType,
        exportName: this.reportInfo.exportName,
        rebuildDateFrom: globals().getClonedValue(this.model.rebuildDateFrom),
        rebuildDateTo: globals().getClonedValue(this.model.rebuildDateTo),
        rebuildMonthFrom: globals().getClonedValue(this.model.rebuildMonthFrom),
        rebuildMonthTo: globals().getClonedValue(this.model.rebuildMonthTo),
        isMonthlyIncluded: globals().getClonedValue(this.monthlySelected) | this.reportInfo.reportRangeType === constants.reportRangeTypes.month
      }

      this.$emit('rebuildExportSettings', apiModel)
      this.setDefaultValue()
    },
    onRebuildMonthFromChange (newValue) {
      let options = []
      let date = moment(newValue).endOf('month')
      for (date; date <= moment().add(-1, 'M').endOf('month'); date.add(1, 'M')) {
        options.push(
          {
            value: date.endOf('month').format('MM/DD/YYYY HH:mm'),
            text: date.format('MMMM, YYYY')
          }
        )
      }

      this.model.rebuildMonthTo = options[0].value
      this.rangeMonthToOptions = options.reverse()
    },
    setDefaultValue () {
      this.monthlySelected = false
      this.model = {
        rebuildDateFrom: moment().format('MM/DD/YYYY'),
        rebuildDateTo: moment().format('MM/DD/YYYY'),
        rebuildMonthFrom: moment().add(-1, 'M').startOf('month').format('MM/DD/YYYY HH:mm'),
        rebuildMonthTo: moment().add(-1, 'M').endOf('month').format('MM/DD/YYYY HH:mm')
      }
    }
  }
}
</script>
