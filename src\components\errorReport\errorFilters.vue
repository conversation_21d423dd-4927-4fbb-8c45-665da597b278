<template>
  <b-row>
    <b-col class="my-2 py-2" xl="3" lg="2" md="4" sm="12" xs="12">
      <b-form-checkbox-group
        id="error-categories"
        v-model="errorReportTypes"
        :options="errorReportTypeOptions"
        value-field="text"
        name="error-categories"
      >
      </b-form-checkbox-group>
    </b-col>
    <b-col class="my-2" xl="3" lg="3" md="4" sm="6" xs="12">
      <b-input-group class="flex-nowrap">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          ref="timeFrom"
          v-model="filters.dateFrom"
          :options="filterTimeOptions"
          format="MM/DD/YYYY HH:mm A"
          placeholder="Date From"
          className="form-control"
          @change="onTimeFromInputChange"
        />
        <b-input-group-append
          is-text
          v-show="filters.dateFrom"
          @click="filters.dateFrom = null"
        >
          <i class="ion ion-md-close"></i>
        </b-input-group-append>
      </b-input-group>
    </b-col>
    <b-col class="my-2" xl="3" lg="3" md="4" sm="6" xs="12">
      <b-input-group class="flex-nowrap">
        <b-input-group-prepend is-text>
          <i class="ion ion-md-calendar" slot="prepend"></i>
        </b-input-group-prepend>
        <date-time-picker
          ref="timeTo"
          v-model="filters.dateTo"
          :options="filterTimeOptions"
          format="MM/DD/YYYY HH:mm A"
          placeholder="Date To"
          className="form-control"
          @change="onTimeToInputChange"
        />
        <b-input-group-append
          is-text
          v-show="filters.dateTo"
          @click="filters.dateTo = null"
        >
          <i class="ion ion-md-close"></i>
        </b-input-group-append>
      </b-input-group>
    </b-col>
    <b-col class="my-2 d-flex justify-content-center justify-content-lg-start" xl="3" lg="4" md="12" sm="12" xs="12">
      <b-btn @click="onOpenMongoScriptModal" variant="success">Run</b-btn>
    </b-col>
  </b-row>
</template>

<script>
import moment from 'moment'
import {errorReportTypesArray} from '@/shared/errorReport/constants'

export default {
  name: 'error-filters',
  props: {
    filters: { type: Object, required: true },
    mongoQueryCommand: { type: Object, required: true }
  },
  data () {
    return {
      filterTimeOptions: {
        autoUpdateInput: false,
        autoApply: true,
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        maxDate: moment().endOf('day')
      },
      errorReportTypes: []
    }
  },
  components: {
    'date-time-picker': () => import('@gravitano/vue-date-range-picker/src/components/DateRangePicker')
  },
  created () {
    if (this.filters.errorreporttypes) {
      this.errorReportTypes = this.filters.errorreporttypes.split(',')
    }
  },
  computed: {
    errorReportTypeOptions () {
      return errorReportTypesArray
    },
    refDateTimeFrom () {
      return (this.$refs.timeFrom || {}).$el || {}
    },
    refDateTimeTo () {
      return (this.$refs.timeTo || {}).$el || {}
    }
  },
  methods: {
    onTimeFromInputChange (newVal) {
      this.refDateTimeFrom.value = newVal || this.filters.dateFrom || null
      this.mongoQueryCommand.dateFrom = this.refDateTimeFrom.value
    },
    onTimeToInputChange (newVal) {
      this.refDateTimeTo.value = newVal || this.filters.dateTo || null
      this.mongoQueryCommand.dateTo = this.refDateTimeTo.value
    },
    onOpenMongoScriptModal () {
      this.$emit('openMongoScriptModal')
    }
  },
  watch: {
    'errorReportTypes': {
      deep: true,
      handler: function () {
        this.filters.errorreporttypes = this.errorReportTypes.join(',')
        this.mongoQueryCommand.categories = this.errorReportTypes
      }
    }
  }
}
</script>
