<template>
  <details-section title="Vehicle Overview" @cancel="onCancel" v-model="mode" :visible="isDisplayed" @visibilityChange='onVisibilityChange' v-if="vehicle">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row title="Stock #" :text="vehicle.stockNumber"/>

      <auto-detail-row title="Title / Condition" :text="getVehicleTitleType"/>

      <auto-detail-row :title="getOdometerTitle" :text="vehicle.miscellaneousVehicleDetails.mileage"/>

      <auto-detail-row title="VIN" :text="vehicle.vin"/>

      <auto-detail-row id="inventory-details-yearmakemodel" title="Year / Make / Model" :text="yearMakeModel"/>

      <!-- Don't use component with :is, safari blink issue-->
      <vehicle-overview-atv v-if="vehicle.vehicleType === vehicleTypes.Atv.value" mode="view"/>

      <vehicle-overview-boat v-if="vehicle.vehicleType === vehicleTypes.Boat.value" mode="view"/>

      <vehicle-overview-bus-scooter-plane v-if="[vehicleTypes.Bus.value,vehicleTypes.Scooter.value,vehicleTypes.Plane.value].includes(vehicle.vehicleType)" mode="view"/>

      <vehicle-overview-motorcycle v-if="vehicle.vehicleType === vehicleTypes.Motorcycle.value" mode="view"/>

      <vehicle-overview-passenger v-if="vehicle.vehicleType === vehicleTypes.Passenger.value" mode="view"/>

      <vehicle-overview-rv v-if="vehicle.vehicleType === vehicleTypes.Rv.value" mode="view"/>

      <vehicle-overview-snow v-if="vehicle.vehicleType === vehicleTypes.Snow.value" mode="view"/>

      <vehicle-overview-truck v-if="vehicle.vehicleType === vehicleTypes.Truck.value" mode="view"/>

      <vehicle-overview-misc v-if="vehicle.vehicleType === vehicleTypes.Misc.value" mode="view"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row  title="Stock #" v-model="vehicle.stockNumber" validation-rule="max:30|xml"/>

      <auto-detail-row title="Title / Condition" v-model="vehicle.miscellaneousVehicleDetails.titleTypeId" :options="metadata.titleTypeOptions"/>

      <auto-detail-row :title="getOdometerTitle" v-model="vehicle.miscellaneousVehicleDetails.mileage" validation-rule="max_value:15000000|min_value:0"/>

      <auto-detail-row title="VIN" :text="vehicle.vin"/>

      <!-- Don't use component with :is, safari blink issue-->
      <vehicle-overview-atv v-if="vehicle.vehicleType === vehicleTypes.Atv.value" mode="edit"/>

      <vehicle-overview-boat v-if="vehicle.vehicleType === vehicleTypes.Boat.value" mode="edit"/>

      <vehicle-overview-bus-scooter-plane v-if="[vehicleTypes.Bus.value,vehicleTypes.Scooter.value,vehicleTypes.Plane.value].includes(vehicle.vehicleType)" mode="edit"/>

      <vehicle-overview-motorcycle v-if="vehicle.vehicleType === vehicleTypes.Motorcycle.value" mode="edit"/>

      <vehicle-overview-passenger v-if="vehicle.vehicleType === vehicleTypes.Passenger.value" mode="edit"/>

      <vehicle-overview-rv v-if="vehicle.vehicleType === vehicleTypes.Rv.value" mode="edit"/>

      <vehicle-overview-snow v-if="vehicle.vehicleType === vehicleTypes.Snow.value" mode="edit"/>

      <vehicle-overview-truck v-if="vehicle.vehicleType === vehicleTypes.Truck.value" mode="edit"/>

      <vehicle-overview-misc v-if="vehicle.vehicleType === vehicleTypes.Misc.value" mode="edit"/>

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../../helpers/autoDetailRow'

import vehicleOverviewAtv from './vehicleOverview-ATV'
import vehicleOverviewPassenger from './vehicleOverview-passenger'
import vehicleOverviewBusScooterPlane from './vehicleOverview-bus-scooter-plane'
import vehicleOverviewMisc from './vehicleOverview-misc'
import vehicleOverviewMotorcycle from './vehicleOverview-motorcycle'
import vehicleOverviewSnow from './vehicleOverview-snow'
import vehicleOverviewTruck from './vehicleOverview-truck'
import vehicleOverviewRv from './vehicleOverview-RV'
import vehicleOverviewBoat from './vehicleOverview-boat'
import vehicleTypes from '../../../../shared/common/vehicle/vehicleTypes'

export default {

  name: 'vehicle-overview',
  data () {
    return {
      mode: 'view',
      isDisplayed: true,
      vehicleTypes: vehicleTypes
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    getVehicleTitleType () {
      return this.metadata.titleTypeOptions[this.vehicle.miscellaneousVehicleDetails.titleTypeId] + ' Title'
    },
    yearMakeModel () {
      return `${this.vehicle.year} ${this.vehicle.make} ${this.vehicle.model}`
    },
    getOdometerTitle () {
      return this.vehicle.vehicleType === vehicleTypes.Plane.value ? 'Hours' : 'Mileage'
    }
  },
  methods: {
    onVisibilityChange (val) {
      this.isDisplayed = val
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'custom-select': selectWithCustomValue,
    'auto-detail-row': autoDetailRow,
    vehicleOverviewAtv,
    vehicleOverviewPassenger,
    vehicleOverviewBusScooterPlane,
    vehicleOverviewMisc,
    vehicleOverviewMotorcycle,
    vehicleOverviewSnow,
    vehicleOverviewTruck,
    vehicleOverviewRv,
    vehicleOverviewBoat
  }
}
</script>
