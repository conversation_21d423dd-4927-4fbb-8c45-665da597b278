<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="National Listing Preference (Auction & Fixed-Price Listings)" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row :large-payload-width="true">
        <span slot="title">Listing Priority:</span>
        <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasToFillAuctionsFirst" :options="getListingPriorityOptions" slot="payload"></b-form-select>
        <span slot="payload" v-else>{{getListingPriorityDesc}}</span>
      </detail-row>
      <detail-row title-position="start" :extra-large-payload-width="true">
        <span slot="title">Launch Time:</span>
        <div slot="payload">
          <b-row>
            <b-col sm="12" md="4" lg="4">
              <div class="d-flex flex-column">
                <b-form-radio v-model="settingsToUpdate.IsLaunchTimeFlexible" class="mt-2" @change="settingsToUpdate.IsLaunchTimeFlexible = true" value="true" name="launch-time-specific" :disabled="isViewMode">
                  Flexible: Auto Sync
                </b-form-radio>
                <b-form-text class="text-dark custom-text-nowrap">Between 9:00AM - 6:00PM PST.</b-form-text>
              </div>
            </b-col>
            <b-col>
              <b-row>
                <b-col cols="12">
                  <b-row>
                    <b-col>
                      <div class="d-flex flex-column flex-md-row">
                        <div class="d-flex flex-row">
                          <b-form-radio class="mt-2" v-model="settingsToUpdate.IsLaunchTimeFlexible" @change="settingsToUpdate.IsLaunchTimeFlexible = false" value="false" name="launch-time-specific" :disabled="isViewMode">
                          </b-form-radio>
                          <span class="text-nowrap mt-2 mr-2">Scheduled: launch Daily at</span>
                          <span class="mt-2" v-if="isViewMode">{{getTime}}</span>
                        </div>
                        <div v-if="!isViewMode" class="d-flex flex-row">
                          <b-form-select v-model="settingsToUpdate.StartHour" class="select-time-item" :options="getHourOptions"></b-form-select>
                          <span class="mt-2 mr-2">:</span>
                          <b-form-select v-model="settingsToUpdate.StartMinute" class="select-time-item" :options="getMinuteOptions"></b-form-select>
                          <b-form-select v-model="settingsToUpdate.AmPmOption" class="select-time-item" :options="getAmPmOptions"></b-form-select>
                          <span class="mt-2 d-none d-lg-block text-nowrap">(Pacific Time)</span>
                        </div>
                      </div>
                    </b-col>
                  </b-row>
                </b-col>
                <b-col cols="12">
                  <b-form-text class="text-dark custom-text-nowrap">If a vehicle is archived between scheduled and launch, it will not be replaced the next day</b-form-text>
                </b-col>
              </b-row>
            </b-col>
          </b-row>
        </div>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Archive Alert Contact:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.ArchiveAlertContactId" :options="getContactOptions"></b-form-select>
          <span v-else>{{getContactDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            This contact will receive an email if there is an active listing that prevents a vehicle from being auto-archived.
            <br>
            When this contact receives the email, he or she is responsible for contacting eBay to end the auction so the vehicle can be archived
            <br>
            If the dealer requests that the email go to an address not listed in the drop-down, they must create a contact in CP so it can be selected here.
          </b-form-text>
        </b-form-group>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import globals from '../../../globals'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      availableContacts: [],
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  created () {
    this.populateAvailableContacts()
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  computed: {
    getContactOptions () {
      return this.availableContacts.map(x => {
        return {
          text: x.FullName,
          value: x.ContactId
        }
      })
    },
    getListingPriorityOptions () {
      return [
        {value: true, text: 'Fill Auctions First'},
        {value: false, text: 'Fill Fixed Price Listings First'}
      ]
    },
    getMinuteOptions () {
      return Array(12).fill().map((_, i) => {
        if (i === 0) {
          return {
            value: 0,
            text: '00'
          }
        }
        if (i === 1) {
          return {
            value: 5,
            text: '05'
          }
        }
        return {
          value: i * 5,
          text: `${i * 5}`
        }
      })
    },
    getAmPmOptions () {
      return [{value: 'am', text: 'AM'}, {value: 'pm', text: 'PM'}]
    },
    getHourOptions () {
      return constants.hourOptions
    },
    getListingPriorityDesc () {
      return (this.getListingPriorityOptions.find(x => x.value === this.settings.HasToFillAuctionsFirst) || {text: '-'}).text
    },
    getContactDesc () {
      return (this.availableContacts.find(x => x.ContactId === this.settings.ArchiveAlertContactId) || {FullName: '-'}).FullName
    },
    getTime () {
      let hour = this.settings.StartHour
      let timePrefix = (this.settings.AmPmOption || 'AM').toUpperCase()
      let minute = this.settings.StartMinute >= 10
        ? this.settings.StartMinute : `0${this.settings.StartMinute}`

      return `${hour}:${minute} ${timePrefix}`
    }
  },
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateAutomatedNationalListingPreferenceSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('National Listing Preference Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.exception(ex, 'Something went wrong!')
        if (ex.response && ex.response.status !== 400) {
          this.$logger.handleError(ex, 'Cannot update eBay automated national listing preference settings', this.settingsToUpdate)
        }
      }).finally(() => {
        this.isViewMode = true
        this.isUpdatingProcessed = false
        this.$emit('refresh')
      })
    },
    populateAvailableContacts () {
      this.$store.dispatch('eBay/getAvailableContacts', { accountId: this.$route.params.accountId }).then(res => {
        this.availableContacts = res.data.ContactOptions || []
      }).catch(ex => {
        this.$logger.handleError(ex, 'Cannot populate available contacts')
      })
    }
  }
}
</script>

<style scoped>
.select-time-item {
  width: 80px;
  margin-right: 0.5rem;
}

@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}

@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}

@media (max-width: 380px) {
  .select-time-item {
    width: 40px;
    margin-left: 0;
    margin-right: 0.5rem;
  }
}
</style>
