<template>
  <div>
    <b-card no-body class="vehicle-description">
    <b-row no-gutters>
      <div>
        <b-card-img :src="getVehicleImgUrl" style="width: 100px" alt="Image" class="rounded-0"></b-card-img>
      </div>
      <div>
        <b-card-body :title="getVehicleTitle" class="ml-2 p-0">
          <p class="text-muted">{{vehicle.Trim}} Stock# {{vehicle.StockNumber}}</p>
        </b-card-body>
      </div>
    </b-row>
  </b-card>
  </div>
</template>

<script>
export default {
  props: {
    vehicle: { type: Object, required: true }
  },
  computed: {
    getVehicleImgUrl () {
      return this.vehicle.PresentationPhoto
    },
    getVehicleTitle () {
      return `${this.vehicle.Year} ${this.vehicle.Make} ${this.vehicle.Model}`
    }
  }
}
</script>

<style scoped>
  .vehicle-description {
    max-width: 540px;
    overflow: hidden;
    background: none;
    border: none;
  }
</style>
