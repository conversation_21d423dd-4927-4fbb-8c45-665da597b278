<template>
  <b-row class='mt-3 w-100'>
    <b-col sm="3">
      <span class="text-muted" size="sm">{{title}}:</span><font-awesome-icon class='ml-1' v-if='!viewMode' @click='add' icon="plus-square"/>
    </b-col>
    <b-col sm="9">
      <div v-for='(item, index) in data' :key='index'>
        <b-row class='ml-1 mt-1'>
          <ValidationProvider tag="b-col" :rules="validateRules" v-slot="{ errors }">
            <b-form-input :name='title+index' @input="update" :state="errors[0] ? false : null" size="sm" class='custom-multiInput float-left' v-model='data[index]' :value='item' :disabled='viewMode'></b-form-input><font-awesome-icon class='float-left mt-1 ml-1' v-if='!viewMode' @click='remove(index)' icon="minus-square"/>
          </ValidationProvider>
        </b-row>
      </div>
    </b-col>
  </b-row>
</template>

<script>
export default {
  name: 'multi-input',
  props: {
    data: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    viewMode: {
      type: Boolean,
      required: true
    },
    validateRules: {
      type: String
    }
  },
  data () {
    return {

    }
  },
  methods: {
    add () {
      this.data.push('')
    },
    remove (index) {
      this.data.splice(index, 1)
    },
    update () {
      this.$emit('input', this.data)
    }
  }
}
</script>

<style scoped>
 @media(min-width: 1600px) {
    .custom-multiInput {
      width: 25%;
    }
 }

 @media(max-width: 1600px) {
    .custom-multiInput {
      width: 30%;
    }
 }

 @media(max-width: 1200px) {
    .custom-multiInput {
      width: 40%;
    }
 }

 @media(max-width: 800px) {
    .custom-multiInput {
      width: 50%;
    }
 }
 @media(max-width: 400px) {
    .custom-multiInput {
      width: 100%;
    }
 }
</style>
