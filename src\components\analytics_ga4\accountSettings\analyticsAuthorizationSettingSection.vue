<template>
  <editSettingsHelper title="eBiz Client Reporting Settings" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode" @save="saveSettings" @cancel="cancel" @changeMode="changeMode">
    <template v-if="!isExceptionOccurred" slot="settings-content">
      <template v-if="!isLoading">
        <ValidationProvider name="Property ID" rules="numeric" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :error="errors[0]">
            <span slot="title">Property ID:</span>
            <template slot="payload">
              <span v-if="isViewMode">{{originalSettings.propertyId}}</span>
              <b-form-input v-else v-model="settingToUpdate.propertyId"></b-form-input>
            </template>
          </detail-row>
        </ValidationProvider>
      </template>
      <loader v-else size="md" class="mt-2"/>
    </template>
    <template v-else slot="settings-content">
      <error-alert></error-alert>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import loader from '../../_shared/loader.vue'
import detailRow from '@/components/details/helpers/detailRow'
import { ga4AccountSettingsService } from '@/services/analytics/AccountSettingsService'
import globals from '../../../globals'

export default {
  name: 'analytics-authorization-settings-section',
  props: {
    accountId: { type: Number, required: true }
  },
  data () {
    return {
      isExceptionOccurred: false,
      isLoading: true,
      isUpdatingProcessing: false,
      isViewMode: true,
      originalSettings: {},
      settingToUpdate: {}
    }
  },
  created () {
    this.populateSettings()
  },
  components: {
    loader,
    editSettingsHelper,
    detailRow
  },
  methods: {
    saveSettings () {
      this.updateSettings()
    },
    cancel () {
      this.resetSettingsToUpdate()
      this.changeMode(true)
    },
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    updateSettings () {
      this.isUpdatingProcessing = true
      ga4AccountSettingsService.updateAccountSettings(this.accountId, this.settingToUpdate).then(res => {
        this.$toaster.success('Settings was updated successfully', {timeout: 5000})
      }).catch(ex => {
        if (ex.response && ex.response.data && ex.response.data.executionResultMessage && ex.response.status === 400) {
          this.$toaster.error(ex.response.data.executionResultMessage, {timeout: 20000})
        } else {
          this.$toaster.error('Something went wrong on updating settings!', {timeout: 5000})
          this.$logger.handlerError(ex, 'Failed on updating analytic account settings')
        }
      }).finally(() => {
        this.isUpdatingProcessing = false
        this.changeMode(true)
        this.populateSettings()
      })
    },
    populateSettings () {
      ga4AccountSettingsService.getAccountSettings(this.accountId).then(res => {
        this.originalSettings = res.data.model
        this.resetSettingsToUpdate()
      }).catch(ex => {
        this.isExceptionOccurred = true
        this.$toaster.error('Something went wrong', {timeout: 5000})
        this.$logger.handleError(ex, 'Failed on receiving analytic account settings from server')
      }).finally(() => {
        this.isLoading = false
      })
    },
    resetSettingsToUpdate () {
      this.settingToUpdate = globals().getClonedValue(this.originalSettings)
    }
  }
}
</script>
