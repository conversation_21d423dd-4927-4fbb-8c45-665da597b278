<template>
  <div class="dark-container analytics-range-selector">
    <rangeSelector
      :custom-label="label"
      :value="range"
      :max-range="maxRange"
      :ranges="ranges"
      @input="onDateChange"
    />
  </div>
</template>

<style src="../../../stylesGlobal/dateRangePicker/darkTheme.scss" lang="scss"></style>

<script>
import rangeSelector from '../../_shared/dateRangeSelector/rangeSelector'
import rangeHelper from './rangeHelper'

export default {
  props: {
    value: Array
  },
  data () {
    return {
      label: '',
      maxRange: rangeHelper.biggestRange,
      ranges: rangeHelper.asNameRangeObject()
    }
  },
  computed: {
    range () {
      return this.value ? this.value : rangeHelper.defaultRange.asFormattedStrings()
    }
  },
  methods: {
    onDateChange (rangeInfo) {
      let rangeName = (rangeHelper
        .rangePresets
        .find(x => x.fromFormatted === rangeInfo.range[0] && x.toFormatted === rangeInfo.range[1]) || {})
        .label

      this.label = rangeName

      this.$emit('input', {
        ...rangeInfo,
        name: rangeName,
        isDefaultRange: rangeHelper.isDefaultRangeLabel(rangeName),
        isBiggestRange: rangeHelper.isBiggestRangeLabel(rangeName)
      })
    }
  },
  components: {
    'rangeSelector': rangeSelector
  }
}
</script>

<style>
  .analytics-range-selector .daterangepicker {
    min-width: 160px;
  }
</style>
