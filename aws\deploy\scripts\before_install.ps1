$applicationName = "EBizAutos.Apps.WebApp"
$destination = "C:\EBizAutos\Web\$applicationName"
$backupRootPath = "C:\EBizAutos\_Backup\Web\$applicationName"
$backupPath = $backupRootPath + "\" + [guid]::NewGuid().ToString()
$ignoreFilesMask = ""

## Create a C# function to perform the service folder delete correctly
$script = @"
using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Linq;

namespace Powershell {
	public static class IOExtensions {
		public static void EmptyDirectory(string path, string filesToIgnoreMask) {
			if (!Directory.Exists(path)) {
				Console.WriteLine("Directory " + path + " does not exist");

				Console.WriteLine("Create directory " + path);
				try {
					Directory.CreateDirectory(path);
				} catch (Exception ex) {
					Console.WriteLine("Error on create directory " + ex.Message);
					throw new Exception("Error on create directory " + path, ex);
				}

				Console.WriteLine("Directory created " + path);
			} else {
				DirectoryInfo directory = new DirectoryInfo(path) {
					Attributes = FileAttributes.Normal
				};

				try {
					foreach (FileSystemInfo info in directory.GetFileSystemInfos("*", SearchOption.AllDirectories)) {
						info.Attributes = FileAttributes.Normal;
					}

					Console.WriteLine("Try delete directory files " + path);
					foreach (FileInfo file in directory.GetFiles()) {
						if (string.IsNullOrEmpty(filesToIgnoreMask) || !Regex.IsMatch(file.Name, filesToIgnoreMask)) {
							file.Delete();
						}
					}

					Console.WriteLine("Try delete directory subdirectories " + path);
					foreach (DirectoryInfo dir in directory.GetDirectories()) {
						dir.Delete(true);
					}

					Console.WriteLine("Directory " + path + " was cleaned successfully");
				} catch (Exception ex) {
					Console.WriteLine("Error on clean directory " + ex.Message);
					throw new Exception("Error on clean directory " + path, ex);
				}
			}
		}

		public static void CopyDirectory(string source, string destination, bool hasToThrowExceptionOnNoSource = true) {
			if (source.Contains("\\\\")) {
				Console.WriteLine("Invalid source path " + source);
				throw new Exception("Invalid source path " + source);
			}

			if (!Directory.Exists(source)) {
				Console.WriteLine("Directory " + source + " does not exist");
				if (hasToThrowExceptionOnNoSource) {
					throw new Exception("Directory " + source + " does not exist");
				}

				return;
			}

			if (!Directory.Exists(destination)) {
				try {
					Directory.CreateDirectory(destination);
				} catch (Exception ex) {
					Console.WriteLine("Error on create directory " + ex.Message);
					throw new Exception("Error on create directory " + destination, ex);
				}
			}

			string[] files = Directory.GetFiles(source);
			foreach (string file in files) {
				string name = Path.GetFileName(file);
				string dest = Path.Combine(destination, name);
				File.Copy(file, dest);
			}

			string[] folders = Directory.GetDirectories(source);
			foreach (string folder in folders) {
				string name = Path.GetFileName(folder);
				string dest = Path.Combine(destination, name);
				CopyDirectory(folder, dest);
			}
		}

		public static void CleanupOldBackups(string backupFolder, int backupsLimit) {
			DirectoryInfo backupDirectory = new DirectoryInfo(backupFolder);
			if (!backupDirectory.Exists) {
				Console.WriteLine("Backup directory " + backupDirectory.FullName + " does not exist");
				return;
			}

			DirectoryInfo[] query = backupDirectory.GetDirectories();
			foreach (DirectoryInfo directory in query.OrderByDescending(x => x.CreationTime).Skip(backupsLimit)) {
				try {
					foreach (FileSystemInfo info in directory.GetFileSystemInfos("*", SearchOption.AllDirectories)) {
						info.Attributes = FileAttributes.Normal;
					}

					Console.WriteLine("Try delete directory " + directory.FullName);
					directory.Delete(true);
					Console.WriteLine("Directory " + directory.FullName + " deleted successfully");
				} catch (Exception ex) {
					Console.WriteLine("Error on delete directory " + ex.Message);
					throw new Exception("Error on delete directory " + directory.FullName, ex);
				}
			}
		}
	}
}
"@

Add-Type -TypeDefinition $script -Language CSharp

Write-Host "---------------------------------- MAKE APPLICATION BACKUP ---------------------------------------------------"
[Powershell.IOExtensions]::CleanupOldBackups($backupRootPath, 2);
[Powershell.IOExtensions]::CopyDirectory($destination, $backupPath, $false);

Write-Host "---------------------------------- CLEAN UP APPLICATION DIRECTORY --------------------------------------------"
[Powershell.IOExtensions]::EmptyDirectory($destination, $ignoreFilesMask);

Write-Host "--------------------------------------------------------------------------------------------------------------"
Write-Host "------------------------------------COMPLETED-----------------------------------------------------------------"
Write-Host "--------------------------------------------------------------------------------------------------------------"
