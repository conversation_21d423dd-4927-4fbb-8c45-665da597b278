<template>
  <div>
    <b-row class="change-marging">
      <h4 v-if="isLoaded && itemsTotalCount > 0">Craigslist Logs {{accountName}} ({{accountId}})</h4>
      <!-- Pagination -->
      <paging
      :pageNumber="filter.page"
      :pageSize="filter.pageSize"
      :totalItems="itemsTotalCount"
      @numberChanged="pageChanged"
      @changePageSize="changePageSize" />
    </b-row>
    <!-- Filters -->
    <b-form v-on:submit.prevent="applySearch">
      <b-card no-body>
        <b-card-body>
            <div class="form-row">
            <b-col lg="6">
              <b-input-group>
                <b-form-input ref="search" max='200' v-model="filter.search" placeholder="Search Stock#, VIN, IDD"/>
                <b-input-group-append>
                  <b-btn type="submit" variant="primary">Submit</b-btn>
                </b-input-group-append>
              </b-input-group>
            </b-col>
          </div>
        </b-card-body>
      </b-card>
    </b-form>
    <!-- Listing -->
    <b-card>
      <b-card no-body>
        <!-- Table -->
        <div class="table-responsive" v-if="isLoaded && itemsTotalCount > 0">
          <b-table
            :items="items"
            :fields="tableFields"
            :sort-by="tableSortBy"
            :sort-desc="tableSortDesc"
            @sort-changed="onSortChanged"
            :striped="true"
            :bordered="false"
            :no-sort-reset="true"
            :no-local-sorting="true"
            responsive
            class="products-table card-table"
          >
            <template #cell(vehicle)="row">
              <div class="media align-items-center">
                <img class="d-block float-left ui-w-50 mr-3" style="min-height: 1px" :src="row.item.item.postFirstPhotoUrl">
                <span>{{row.item.title}}</span>
              </div>
            </template>

            <template #cell(show_details)="row">
              <div class="media align-items-center">
                <b-button size="sm" @click.stop="row.toggleDetails" class="text-center">
                  {{ row.detailsShowing ? 'Hide' : 'Show' }} Details
                </b-button>
              </div>
            </template>
            <template #row-details="row">
              <b-card>
                <b-row v-for="value in detailFields" :key="value.Key" class="mb-2">
                  <b-col sm="12" md="4" lg="3" xl="2">
                    <b>{{value.Label}}</b>
                  </b-col>
                  <b-col>
                    {{row.item.item[value.Key]}}
                  </b-col>
                </b-row>
              </b-card>
            </template>

            <template #cell(postedBy)="row">
              <div v-html="getPostedByDescription(row.item)"></div>
            </template>

            <template #cell(status)="row">
              <span v-if='row.item.postingLog.craigslistTask.status === 6' class='text-success'>
                {{getStatusLabel(row.item.postingLog.craigslistTask.status)}}
              </span>
              <span v-else class='text-danger'>
                {{getStatusLabel(row.item.postingLog.craigslistTask.status)}}
              </span>
            </template>

            <template #cell(manage)="row">
              <template>
                <b-dropdown variant="outline-secondary icon-btn btn-round" size="sm" right no-caret boundary='viewport'>
                  <template slot="button-content">
                    <i class="ion ion-ios-more m-0"></i><span class="sr-only">Manage</span>
                  </template>
                  <b-dropdown-item :to="getDetailLogPath(row)">Detail log</b-dropdown-item>
                  <b-dropdown-item v-if='row.item.isRepostPossible && !row.item.isDisabledPosting' :to="getPostPath(row)">Repost</b-dropdown-item>
                </b-dropdown>
              </template>
            </template>
          </b-table>
        </div>
        <div class="ml-4" v-else-if="!isLoaded">
          Loading...
        </div>
        <div class="ml-4" v-else>
          No logs found
        </div>
      </b-card>
        <!-- / Pagination -->
      <paging
        :pageNumber="filter.page"
        :pageSize="filter.pageSize"
        :totalItems="itemsTotalCount"
        titled
        pageSizeSelector
        @numberChanged="pageChanged"
        @changePageSize="changePageSize" />
    </b-card>
  </div>
</template>

<script>
import Paging from '@/components/_shared/paging.vue'
import numeral from 'numeral'
import { ObjectSchema } from '@/shared/common/objectHelpers'
import QueryStringHelper from '@/shared/common/queryStringHelper'
import CraigslistDescriptionHelper from '@/shared/craigslist/craigslistDescriptionHelper'
import DetailLogFields from '@/shared/craigslist/detailLogFields'
import Constants from '@/shared/craigslist/constants'
import moment from 'moment'

const defaultValues = new ObjectSchema({
  pageSize: { type: Number, default: 25 },
  search: { type: String, default: '' },
  page: { type: Number, default: 1 },
  sort: { type: Number, default: 6 }
})
const queryHelper = new QueryStringHelper(defaultValues)
const craigslistDescriptionHelper = new CraigslistDescriptionHelper()

export default {
  name: 'craigslist-log-listing',
  metaInfo: {
    title: 'Craigslist log'
  },
  props: {
    accountId: {
      type: Number,
      required: true
    }
  },
  components: {
    'paging': Paging
  },
  data () {
    return {
      isLoaded: false,
      filter: defaultValues.getObject(),
      accountName: '',
      itemsTotalCount: 0,
      items: [],
      detailFields: DetailLogFields
    }
  },
  created () {
    this.filter = queryHelper.parseQueryStringToObject(this.$router)
    if (this.accountId) {
      this.loadContent()
    }
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'vehicle',
          sortable: false,
          label: 'Vehicle',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'show_details',
          sortable: false,
          label: '',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'status',
          sortable: false,
          label: 'Status',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'item.price',
          sortable: true,
          sortTypeAsc: Constants.logSortTypes.priceAsc,
          sortTypeDesc: Constants.logSortTypes.priceDesc,
          label: 'Vehicle Price',
          tdClass: 'py-2 align-middle',
          formatter: value => numeral(value).format('$0,0')
        },
        {
          key: 'item.areaDescription',
          sortable: true,
          sortTypeAsc: Constants.logSortTypes.areaAsc,
          sortTypeDesc: Constants.logSortTypes.areaDesc,
          label: 'Area',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            if (value) {
              return value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
            } else {
              return ''
            }
          }
        },
        {
          key: 'loggedDateTime',
          sortable: true,
          sortTypeAsc: Constants.logSortTypes.dateTimeAsc,
          sortTypeDesc: Constants.logSortTypes.dateTimeDesc,
          label: 'Log date',
          tdClass: 'py-2 align-middle',
          formatter: value => {
            return moment(value).format('MM/DD/YYYY hh:mm:ss A')
          }
        },
        {
          key: 'postedBy',
          sortable: false,
          label: 'Posted By',
          tdClass: 'py-2 align-middle'
        },
        {
          key: 'manage',
          sortable: false,
          label: 'Manage',
          tdClass: 'py-2 align-middle'
        }
      ]
    },
    tableSortBy () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.key
      } else {
        return null
      }
    },
    tableSortDesc () {
      const sortedColumn = this.tableFields.find(x => x.sortTypeAsc === this.sortType || x.sortTypeDesc === this.sortType)
      if (sortedColumn) {
        return sortedColumn.sortTypeDesc === this.sortType
      } else {
        return false
      }
    }
  },
  methods: {
    pageChanged (newPage) {
      this.filter.page = newPage
      this.synchronizeUrlAndReload()
    },
    changePageSize (newSize) {
      this.filter.pageSize = newSize
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    onSortChanged (value) {
      const sortingColumn = this.tableFields.find(x => x.key === value.sortBy)
      if (value.sortDesc) {
        this.filter.sort = sortingColumn.sortTypeDesc
      } else {
        this.filter.sort = sortingColumn.sortTypeAsc
      }
      this.synchronizeUrlAndReload()
    },
    applySearch () {
      this.filter.page = 1
      this.synchronizeUrlAndReload()
    },
    getDetailLogPath (data) {
      return `/craigslist/${data.item.accountID}/logs/${data.item.guid}`
    },
    getPostPath (data) {
      return `/craigslist/${data.item.accountID}/post/${data.item.item.vin}/?postingtype=2`
    },
    synchronizeUrlAndReload () {
      queryHelper.rebuildParamsInQueryString(this.$router, this.filter)
      this.loadContent()
    },
    loadContent () {
      this.$store.dispatch('craigslist/getLogs', {accountId: this.accountId, filter: this.filter}).then(x => {
        this.accountName = x.data.model.accountName
        this.items = x.data.model.craigslistLogsEntries
        this.itemsTotalCount = x.data.model.pageInfo.totalEntries
        this.isLoaded = true
      })
        .catch(ex => {
          this.isLoaded = true
          this.$toaster.error(`Can't get logs data for accountId ${this.accountId}`)
          this.$logger.handleError(ex, 'Can\'t get craigslist log listing', {filter: this.filter})
        })
    },
    getStatusLabel (key) {
      return craigslistDescriptionHelper.getCraigslistStatusDescription(key)
    },
    getPostedByDescription (item) {
      if (item.postingSource === 0) { // AutomatedTool
        let descItems = [`<span>Campaign: ${item.campaignName}</span>`]
        if (item.campaignCreatedByUser) {
          descItems.push(`<span>Created by: ${item.campaignCreatedByUser}</span>`)
        }
        if (item.campaignUpdatedByUser) {
          descItems.push(`<span>Last updated by: ${item.campaignUpdatedByUser}</span>`)
        }
        return descItems.join('<br/>')
      }

      return item.postedByUser || craigslistDescriptionHelper.getCraigslistSourceDescription(item.postingSource)
    }
  }
}
</script>
<style lang="scss">
.change-marging {
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}
</style>
