<template>
  <a class="visibility-control underlined noselect" v-if="isLinkVariant" @click="onVisibilityChange" :class="{ 'disabled': disabled}">
    <span class="noselect" v-show="isVisible"><font-awesome-icon class="mr-1" icon="eye" size="sm" />Visible</span>
    <span class="noselect" v-show="!isVisible"><font-awesome-icon class="mr-1" icon="eye-slash" size="sm" />Hidden</span>
  </a>
  <b-button class="visibility-control noselect" v-else-if="isButtonVariant"  size="lg" variant="light" @click="onVisibilityChange" :disabled="disabled">
    <span class="noselect" v-show="isVisible"><font-awesome-icon class="mr-1" icon="eye" size="sm" />Visible</span>
    <span class="noselect" v-show="!isVisible"><font-awesome-icon class="mr-1" icon="eye-slash" size="sm" />Hidden</span>
  </b-button>
  <a class="visibility-control noselect arrow text-muted" v-else-if="isArrowVariant" @click="onVisibilityChange">
    <span class="noselect" v-show="isVisible"><font-awesome-icon class="mr-1" icon="angle-up" size="sm" /></span>
    <span class="noselect" v-show="!isVisible"><font-awesome-icon class="mr-1" icon="angle-down" size="sm" /></span>
  </a>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    disabled: Boolean,
    variant: {
      type: String,
      default: 'link',
      validator (val) {
        return ['link', 'button', 'arrow'].includes(val)
      }
    }
  },
  data () {
    return {
      isVisible: this.visible
    }
  },
  computed: {
    isLinkVariant () { return this.variant === 'link' },
    isButtonVariant () { return this.variant === 'button' },
    isArrowVariant () { return this.variant === 'arrow' }
  },
  methods: {
    onVisibilityChange () {
      if (this.disabled) {
        return
      }
      this.isVisible = !this.isVisible
      this.$emit('visibilityChange', this.isVisible)
    }
  },
  watch: {
    visible (val) {
      this.isVisible = val
    }
  }
}
</script>

<style scoped>
a.visibility-control.underlined {
  text-decoration: underline !important;
}
a.visibility-control {
  color: black;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.8rem;
  white-space: nowrap;
}

.visibility-control.disabled {
  color: grey;
  cursor: not-allowed;
}

.noselect {
  -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
       -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently
                                  supported by Chrome and Opera */
}
</style>
