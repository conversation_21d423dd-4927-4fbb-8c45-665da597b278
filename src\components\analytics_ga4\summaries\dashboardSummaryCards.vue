<template>
  <div class="row no-gutters white-bg border rounded">

    <div class="col-6 col-sm-6 col-xl-3 border-bottom border-top-0 border-right border-left-0 border-xl-bottom-0 border-sm-right">
      <summary-card
        label="Sessions"
        :cardClass="'rounded-0 border-0 m-0'"
        :value="summaryCards.sessions"
        :delta="summaryCards.sessionsDelta"
        :rangeLabel="summaryCards.label">
        <i class="ion ion-ios-people h1 m-0 opacity-25 d-none d-sm-inline"></i>
      </summary-card>
    </div>

    <div class="col-6 col-sm-6 col-xl-3 border-bottom border-top-0 border-right-0 border-left-0 border-xl-bottom-0 border-xl-right">
      <summary-card
        label="Leads"
        :cardClass="'rounded-0 border-0 m-0'"
        :value="summaryCards.totalLeads"
        :delta="summaryCards.totalLeadsDelta"
        :rangeLabel="summaryCards.label">
        <i class="ion ion-ios-chatboxes h1 m-0 opacity-25 d-none d-sm-inline"></i>
      </summary-card>
    </div>

    <div class="col-6 col-sm-6 col-xl-3 border-bottom border-top-0 border-right border-left-0 border-xl-bottom-0 border-sm-right">
      <summary-card
        label="Bounce Rate"
        :cardClass="'rounded-0 border-0 m-0'"
        :value="summaryCards.bounceRate"
        :delta="summaryCards.bounceRateDelta"
        :rangeLabel="summaryCards.label"
        reverse>
        <template slot="value" slot-scope="valueScope">
          {{valueScope.localizedValue}}%
        </template>
        <i class="ion ion-ios-laptop h1 m-0 opacity-25 d-none d-sm-inline"></i>
      </summary-card>
    </div>

    <div class="col-6 col-sm-6 col-xl-3 border-0">
      <summary-card
        label="Engagement"
        :cardClass="'rounded-0 border-0 m-0'"
        :value="summaryCards.avgSessionDuration"
        :delta="summaryCards.avgSessionDurationDelta"
        :rangeLabel="summaryCards.label">
        <template slot="value" slot-scope="valueScope">
          {{$locale.getSecondsDurationFormatted(valueScope.value)}}
        </template>
        <i class="ion ion-ios-time h1 m-0 opacity-25 d-none d-sm-inline"></i>
      </summary-card>
    </div>

  </div>
</template>

<script>
import SummaryCard from '../summaryCard'
export default {
  name: 'dashboard-summary-cards',
  props: {
    summaryCards: { type: Object, required: true }
  },
  components: {
    SummaryCard
  }
}
</script>

<style scoped>

</style>
