<template>
  <div>
    <modifyTitlePrivateCategory v-if="type === reviseOptions.modifyTitlePrivateCategory.key"
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
    />
    <addEBayUpgrades v-if="type === reviseOptions.addEBayUpgrades.key"
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
    />
    <addToItemDescription v-if="type === reviseOptions.addToItemDescription.key"
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
    />
    <reservePrice
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
      v-if="type === reviseOptions.changeReservePrice.key || type === reviseOptions.addReservePrice.key || type === reviseOptions.lowerReservePrice.key || type === reviseOptions.removeReservePrice.key"
      :type="type">
    </reservePrice>
    <binPrice
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
      v-if="type === reviseOptions.addBinPrice.key || type === reviseOptions.lowerBinPrice.key || type === reviseOptions.raiseBinPrice.key || type === reviseOptions.removeBinPrice.key"
      :type="type"
    >
    </binPrice>
    <changeFixedPrice
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
      v-if="type === reviseOptions.changeFixedPrice.key">
    </changeFixedPrice>
    <changeStartingBid
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
      v-if="type === reviseOptions.changeStartingBid.key">
    </changeStartingBid>
    <modifyVehicleDescription
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
      v-if="type === reviseOptions.modifyVehicleDescription.key">
    </modifyVehicleDescription>
    <changeAdvertisedPrice
      :btnDesc="getButtonDescription"
      :reviseHeader="getReviseOptionTitle"
      v-if="type === reviseOptions.changeAdvertisedPrice">
    </changeAdvertisedPrice>
  </div>
</template>

<script>
import constants from '@/shared/ebay/constants'
import modifyTitlePrivateCategory from './modifyTitlePrivateCategory'
import addEBayUpgrades from './addEBayUpgrades'
import addToItemDescription from './addToItemDescription'
import reservePrice from './reservePrice'
import binPrice from './binPrice'
import changeFixedPrice from './changeFixedPrice'
import changeStartingBid from './changeStartingBid'
import modifyVehicleDescription from './modifyVehicleDescription'
import changeAdvertisedPrice from '@/components/eBay/revise/changeAdvertisedPrice'

export default {
  props: {
    type: { type: Number, required: true }
  },
  data () {
    return {
      reviseOptions: constants.reviseOptions
    }
  },
  components: {
    modifyTitlePrivateCategory,
    addEBayUpgrades,
    addToItemDescription,
    reservePrice,
    binPrice,
    changeFixedPrice,
    changeStartingBid,
    modifyVehicleDescription,
    changeAdvertisedPrice
  },
  computed: {
    getReviseOptionTitle () {
      let res = Object.values(this.reviseOptions).find(x => x.key === this.type)
      if (res) {
        return res.title
      }

      return ''
    },
    getButtonDescription () {
      let res = Object.values(this.reviseOptions).find(x => x.key === this.type)
      if (res) {
        return res.buttonDescription
      }

      return ''
    }
  }
}
</script>
