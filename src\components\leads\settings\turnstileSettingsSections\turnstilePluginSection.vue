<template>
  <div class='mb-4'>
    <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Turnstile Plugin Settings" :isDisabled="isDisabled" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
      <div slot="settings-content">
        <detail-row fixedPayloadWidth>
          <span slot="title">Enable Turnstile:</span>
          <b-form-checkbox v-model='turnstileSettingsToUpdate.isTurnstileEnabled' slot="payload" :disabled='isViewMode'>
            Enable Turnstile protection for lead forms
          </b-form-checkbox>
        </detail-row>
        <div v-if='turnstileSettingsToUpdate.isTurnstileEnabled'>
          <detail-row fixedPayloadWidth>
            <span slot="title">Site Key:</span>
            <b-form-input v-model='turnstileSettingsToUpdate.siteKey' placeholder="Enter your Turnstile site key" type="text" slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Secret Key:</span>
            <b-form-input v-model='turnstileSettingsToUpdate.secretKey' placeholder="Enter your Turnstile secret key" type="password" slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row extraLargePayloadWidth>
            <span slot="title">Failure Message:</span>
            <b-form-textarea v-model='turnstileSettingsToUpdate.failureMessage' placeholder="Message to display when Turnstile verification fails" slot="payload" :disabled='isViewMode' rows="3"/>
          </detail-row>
          <detail-row fixedPayloadWidth>
            <span slot="title">Failure Action:</span>
            <b-form-select v-model='turnstileSettingsToUpdate.failureAction' :options='failureActionOptions' slot="payload" :disabled='isViewMode'/>
          </detail-row>
          <detail-row extraLargePayloadWidth>
            <span slot="title">Lead Types:</span>
            <div slot="payload">
              <small class="text-muted d-block mb-2">Configure which lead types should use Turnstile protection</small>
              <b-form-checkbox-group
                v-model="activeLeadTypes"
                :options="leadTypeOptions"
                stacked
                :disabled="isViewMode"
              />
            </div>
          </detail-row>
        </div>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import { mapGetters } from 'vuex'
import { turnstileFailureAction, turnstileAllowedLeadTypes } from '@/shared/leads/common'
import globals from '../../../../globals'

export default {
  name: 'turnstile-plugin-section',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      required: true
    },
    isDisabled: Boolean
  },
  data () {
    return {
      isViewMode: true,
      turnstileSettingsToUpdate: {}
    }
  },
  created () {
    this.initData()
  },
  components: {
    'detail-row': detailRow,
    editSettingsHelper: () => import('@/components/_shared/editSettingsHelper')
  },
  computed: {
    ...mapGetters('leadsAccountSettings', ['turnstileSettings']),
    failureActionOptions () {
      return Object.values(turnstileFailureAction).map(action => ({
        value: action.value,
        text: action.label
      }))
    },
    leadTypeOptions () {
      return Object.values(turnstileAllowedLeadTypes).map(type => ({
        value: type.value,
        text: type.label
      }))
    },
    activeLeadTypes: {
      get () {
        if (!this.turnstileSettingsToUpdate.leadTypeSettings) return []
        return this.turnstileSettingsToUpdate.leadTypeSettings
          .filter(setting => setting.isActive)
          .map(setting => setting.leadType)
      },
      set (values) {
        this.turnstileSettingsToUpdate.leadTypeSettings = Object.values(turnstileAllowedLeadTypes).map(type => ({
          leadType: type.value,
          isActive: values.includes(type.value)
        }))
      }
    }
  },
  methods: {
    initData () {
      if (this.turnstileSettings) {
        this.turnstileSettingsToUpdate = globals().getClonedValue(this.turnstileSettings)
      } else {
        // Initialize with default values
        this.turnstileSettingsToUpdate = {
          accountId: +this.$route.params.accountId,
          isTurnstileEnabled: false,
          siteKey: '',
          secretKey: '',
          failureMessage: '',
          failureAction: 0,
          leadTypeSettings: Object.values(turnstileAllowedLeadTypes).map(type => ({
            leadType: type.value,
            isActive: false
          }))
        }
      }
    },
    saveSettings () {
      if (this.validate()) {
        this.$emit('save', this.turnstileSettingsToUpdate)
        this.isViewMode = true
      }
    },
    changeMode (mode) {
      this.isViewMode = mode
    },
    cancel () {
      this.initData()
      this.changeMode(true)
    },
    validate () {
      if (this.turnstileSettingsToUpdate.isTurnstileEnabled) {
        if (!this.turnstileSettingsToUpdate.siteKey || this.turnstileSettingsToUpdate.siteKey.trim() === '') {
          this.$toaster.error('Site Key field is required when Turnstile is enabled')
          return false
        }
        if (!this.turnstileSettingsToUpdate.secretKey || this.turnstileSettingsToUpdate.secretKey.trim() === '') {
          this.$toaster.error('Secret Key field is required when Turnstile is enabled')
          return false
        }
      }
      return true
    }
  }
}
</script>
