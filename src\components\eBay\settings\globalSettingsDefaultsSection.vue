<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Global Settings & Defaults" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row  titlePosition="start" :large-payload-width="true">
        <span slot="title">Default Listing Type:</span>
        <b-form-select v-if="!isViewMode" slot="payload" v-model="settingsToUpdate.ListingType" :options="getListingTypeOptions"></b-form-select>
        <span slot="payload" v-else>{{getListingTypeDesc}}</span>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Default Duration:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.DurationInDays" :options="getDefaultDurationOptions"></b-form-select>
          <span v-else>{{settings.DurationInDays || '-'}} days</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Default Duration setting applies to Auction and Fixed Price Listing only.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Default Start Time:</span>
        <div slot="payload" v-if="!isViewMode" class="d-flex flex-row">
          <b-form-select v-model="selectedHour" class="select-time-item" @input="onInputHour" :options="getHourOptions"></b-form-select>
          <span class="mt-2 mr-2">:</span>
          <b-form-select v-model="selectedMinute" class="select-time-item" @input="onInputMinute" :options="getMinuteOptions"></b-form-select>
          <b-form-select v-model="selectedTimePrefix" class="select-time-item" @input="onInputTimePrefix" :options="getAmPmOptions"></b-form-select>
          <span class="mt-2 d-none d-sm-block text-nowrap">(Pacific Time)</span>
        </div>
        <span slot="payload" v-else>{{getTime}}</span>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Reserve Price Prompt:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasReservePricePrompt" :options="getYesNoOptions"></b-form-select>
          <span v-else>{{settings.HasReservePricePrompt ? 'Yes' : 'No'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Prompts you when your listing is missing the Reserve Price on the "Schedule eBay Listing" page.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Best Offers Default:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsAllowedBestOffers" :options="getBestOfferOptions"></b-form-select>
          <span v-else>{{settings.IsAllowedBestOffers ? 'Allow Best Offers' : 'Do Not Allow Best Offers'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            This setting determines if Allow Best Offers will be pre-selected on the "Scheduled eBay Listing" page got Fixed Price Listings.
            <br>
            You will be able to change this drop-down on each eBay listing.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Allow Best Offer Auto Decline:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsAllowedBestOfferAutoDecline" :options="getYesNoOptions"></b-form-select>
          <span v-else>{{settings.IsAllowedBestOfferAutoDecline ? 'Yes' : 'No'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            This setting determines if you can set an Auto Decline amount for Beast Offers on Fixed Priced Listings on the "Schedule eBay Listing" page.
            <br>
            Set a default % of list price threshold below.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <ValidationProvider name="Auto Decline Threshold" rules="max_value:100|min_value:0" v-slot="{errors}">
      <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Auto Decline Threshold (%):</span>
        <b-form-input slot="payload" v-if="!isViewMode" name="Auto_Decline_Threshold" min="0" v-model="settingsToUpdate.AutoDeclineThreshold"></b-form-input>
        <span slot="payload" v-else>{{settings.AutoDeclineThreshold || '-'}}</span>
      </detail-row>
      </ValidationProvider>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Private Auctions Default:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsPrivateAuctions" :options="getYesNoOptions"></b-form-select>
          <span v-else>{{settings.IsPrivateAuctions ? 'Yes' : 'No'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Select "Yes" if you prefer to have the identity and email addresses of bidders on your auctions not visible on the auction page.
            <br>
            You will have the ability to override this setting with each listing scheduled.
            <br>
            Please note that changes to this setting will apply to vehicles added to the system today forward.
            <br>
            Existing vehicles will require a change to this setting on the Send to eBay screen (to change the listing's prior selection).
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Default Item Location:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-input v-if="!isViewMode" v-model="settingsToUpdate.ItemLocation"></b-form-input>
          <span v-else>{{settings.ItemLocation || '-'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            City, State (e.g., Las Vegas, NY)
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Full Payment Required In:</span>
        <b-form-group
          slot="payload"
          class="w-100"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.FullPaymentRequiredInDays" :options="getDaysFullPaymentOptions"></b-form-select>
          <span v-else>{{settings.FullPaymentRequiredInDays || '-'}} days</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Number of days that Full Payment from the eBay Buyer is required in.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Multiple eBay Accounts:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasMultipleEbayUsers" :options="getYesNoOptions"></b-form-select>
          <span v-else>{{settings.HasMultipleEbayUsers ? 'Yes' : 'No'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            This setting will allow each sales contact to be able to submit eBay auctions under different eBay accounts.
          </b-form-text>
        </b-form-group>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Send Error Email Notification:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasToSendErrorEmailNotification" :options="getYesNoOptions"></b-form-select>
          <span v-else>{{settings.HasToSendErrorEmailNotification ? 'Yes' : 'No'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Sends you an email immediately when your scheduled eBay listings are rejected by the eBay API.
          </b-form-text>
        </b-form-group>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import constants from '@/shared/ebay/constants'
import ebayOptions from '@/shared/ebay/ebayOptions'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'
import globals from '../../../globals'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      selectedHour: this.settings.StartHour === 0 ? 12 : this.settings.StartHour > 12 ? this.settings.StartHour - 12 : this.settings.StartHour,
      selectedMinute: this.settings.StartMinute,
      selectedTimePrefix: this.settings.StartHour >= 12 ? 'PM' : 'AM',
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  computed: {
    getListingTypeOptions () {
      return Object.values(constants.listingFormatTypes)
    },
    getMinuteOptions () {
      return Array(12).fill().map((_, i) => {
        if (i === 0) {
          return {
            value: 0,
            text: '00'
          }
        }
        if (i === 1) {
          return {
            value: 5,
            text: '05'
          }
        }
        return {
          value: i * 5,
          text: `${i * 5}`
        }
      })
    },
    getAmPmOptions () {
      return ['AM', 'PM']
    },
    getYesNoOptions () {
      return ebayOptions.yesNoOptions
    },
    getHourOptions () {
      return constants.hourOptions
    },
    getDefaultDurationOptions () {
      return [{ value: 3, text: '3 Days' }, { value: 5, text: '5 Days' }, { value: 7, text: '7 Days' }, { value: 10, text: '10 Days' }]
    },
    getBestOfferOptions () {
      return ebayOptions.bestOfferOptions
    },
    getDaysFullPaymentOptions () {
      return [{value: 3, text: '3 Days'}, {value: 7, text: '7 Days'}, {value: 10, text: '10 Days'}, {value: 14, text: '14 Days'}]
    },
    getListingTypeDesc () {
      let res = Object.values(constants.listingFormatTypes).find(x => x.value === (this.settings || {}).ListingType)
      if (res) {
        return res.text
      }

      return 'Undefined'
    },
    getTime () {
      let hour = this.settings.StartHour <= 12 ? this.settings.StartHour === 0 ? 12 : this.settings.StartHour : this.settings.StartHour - 12
      let timePrefix = this.settings.StartHour < 12 ? 'AM' : 'PM'
      let minute = this.settings.StartMinute >= 10
        ? this.settings.StartMinute : `0${this.settings.StartMinute}`

      return `${hour}:${minute} ${timePrefix}`
    }
  },
  mixins: [editSettingsMixin],
  components: {
    editSettingsHelper,
    detailRow
  },
  methods: {
    onInputHour (value) {
      this.settingsToUpdate.StartHour = this.selectedTimePrefix === 'AM' ? value : +value < 12 ? +value + 12 : 0
    },
    onInputMinute (value) {
      this.settingsToUpdate.StartMinute = +value
    },
    onInputTimePrefix (value) {
      if (value === 'AM') {
        this.settingsToUpdate.StartHour = +this.selectedHour === 12 ? 0 : this.selectedHour
      } else {
        this.settingsToUpdate.StartHour = +this.selectedHour < 12 ? +this.selectedHour + 12 : 12
      }
    },
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updateGlobalDefaultSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('Global Settings & Defaults Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay account global and default setting')
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}

@media (max-width: 380px) {
  .select-time-item {
    width: 40px;
    margin-left: 0;
    margin-right: 0.5rem;
  }
}

.select-time-item {
  width: 80px;
  margin-right: 0.5rem;
}
</style>
