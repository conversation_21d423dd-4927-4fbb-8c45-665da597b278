<template>
  <div>
    <b-row class="mb-3">
      <b-col xl="12">
        <b-form-radio-group id="selectedForm" v-model="selectedForm" :options="formOptions" />
      </b-col>
    </b-row>

    <b-row>
      <b-col xl="12">
        <range-form
          v-if="selectedForm === 'standard'"
          :disabled="disabled"
          :year="year"
          :quarter="quarter"
          :month="month"
          actionText="Rebuild"
          @submited="notifyStandardRangeSubmitClicked"
        />
      </b-col>
    </b-row>
    <b-row>
      <b-col xl="12">
        <custom-range-form
          v-if="selectedForm !== 'standard'"
          :disabled="disabled"
          actionText="Rebuild"
          @submitted="notifyCustomRangeSubmitClicked"
        />
      </b-col>
    </b-row>
  </div>
</template>

<script>
export default {
  props: {
    year: Number,
    quarter: Number,
    month: Number,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    'range-form': () => import('./rangeSelector/rangeForm.vue'),
    'custom-range-form': () => import('./customRangeForm.vue')
  },
  data () {
    return {
      selectedForm: 'standard',
      formOptions: [
        { text: 'Standard range', value: 'standard' },
        { text: 'Custom range', value: 'custom' }
      ]
    }
  },
  methods: {
    notifyStandardRangeSubmitClicked (newRange) {
      this.$emit('standardSubmited', newRange)
    },
    notifyCustomRangeSubmitClicked (newRange) {
      this.$emit('customSubmited', newRange)
    }
  }
}
</script>
