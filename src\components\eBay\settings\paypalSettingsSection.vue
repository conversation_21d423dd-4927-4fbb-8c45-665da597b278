<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="PayPal Settings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <ValidationProvider name="Email" rules="email" v-slot="{errors}">
      <detail-row titlePosition="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">PayPal Email:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-input name="Email" type="email" v-if="!isViewMode" v-model="settingsToUpdate.Email"></b-form-input>
          <span v-else>{{settings.Email || '-'}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Enter your PayPal email address to have a PayPal icon appear on your listings. (Required to use Immediate Deposit)
          </b-form-text>
        </b-form-group>
      </detail-row>
      </ValidationProvider>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">PayPal Bidder Verification:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsPayPalRequiredToBid" :options="getPayPalBidderVerificationOptions"></b-form-select>
          <span v-else>{{getPayPalBidderVerificationDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            Requires bidders to have a PayPal account before allowing their bid. Requiring PayPal does not require payment via PayPal.
            <br>
            It only requires that the buyer be a PayPal account holder before they can bid on or purchase the item.
          </b-form-text>
        </b-form-group>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import globals from '../../../globals'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  computed: {
    getPayPalBidderVerificationOptions () {
      return [{value: true, text: 'PayPal Required to Bid'}, {value: false, text: 'PayPal No Required to Bid(default)'}]
    },
    getPayPalBidderVerificationDesc () {
      let res = this.getPayPalBidderVerificationOptions.find(x => x.value === this.settings.IsPayPalRequiredToBid)
      if (res) {
        return res.text
      }

      return 'Undefined'
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  mixins: [editSettingsMixin],
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$store.dispatch('eBay/updatePayPalSettings', { accountId: this.$route.params.accountId, data: this.settingsToUpdate }).then(res => {
        this.$toaster.success('PayPal Settings Updated Successfully')
      }).catch(ex => {
        this.$toaster.error('Something went wrong! Please try again later')
        this.$logger.handleError(ex, 'Cannot update eBay account paypal setting')
      }).finally(() => {
        this.isUpdatingProcessed = false
        this.isViewMode = true
        this.$emit('refresh')
      })
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}
</style>
