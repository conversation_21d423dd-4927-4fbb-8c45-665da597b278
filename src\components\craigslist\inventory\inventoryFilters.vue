<template>
  <div class="position-relative">
    <paging
      class="custom-craiglist-inventory-paging d-none d-md-block"
      :pageNumber="pageNumber"
      :pageSize="pageSize"
      :totalItems="totalItems"
      pageSizeSelector
      @numberChanged="onPageNumberChanged"
      @changePageSize="onPageSizeChanged"
    />

    <b-tabs v-model="selectedCraigslistStatus" class="nav-tabs-top nav-responsive-sm" no-fade>
      <b-tab v-for="(status, index) in inventoryFilters.craigslistStatus.options" :key="`${index}`" :title="`${status.label}`">
        <b-form inline @submit.prevent="onSubmitSearch">
          <b-card-body>
            <div class="form-row">
              <b-col lg="5" md="7" sm="9" xs="12">
                <b-input-group class="inventory-filter__search">
                  <b-form-input placeholder="Search by VIN, Stock or Make/Model" max='200' v-model.trim="inventoryFilters.search.value"></b-form-input>
                  <b-input-group-append>
                    <b-btn type="submit" variant="primary">Submit</b-btn>
                  </b-input-group-append>
                </b-input-group>
              </b-col>

              <b-col lg="5" md="5" sm="3" xs="12" class="text-right mt-3" offset-lg="2">
                <b-link class="toggle-search-filters-link d-none d-sm-block"  v-b-toggle.advanced-search-collapse>
                  <span class="when-opened">
                    Basic search
                  </span>
                  <span class="when-closed">
                    Advanced search
                  </span>
                </b-link>
                <b-btn class="w-100 d-block d-sm-none"  v-b-toggle.advanced-search-collapse>
                  <span class="when-opened">
                    Basic search
                  </span>
                  <span class="when-closed">
                    Advanced search
                  </span>
                </b-btn>
              </b-col>

            </div>
          </b-card-body>
        </b-form>

        <b-collapse v-model="isAdvancedFiltersVisible" id="advanced-search-collapse">
          <div class="border-top ml-4 mr-4"></div>
          <b-form v-on:submit.prevent="onFilterApply">
          <b-card-body>
            <div class="form-row">
              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.condition.value"
                    :options="inventoryFilters.condition.options"
                    @change="onSelectChange($event, inventoryFilters.condition.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.vehicleType.value"
                    :options="inventoryFilters.vehicleType.options"
                    @change="onSelectChange($event, inventoryFilters.vehicleType.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.bodyStyle.value"
                    :options="inventoryFilters.bodyStyle.options"
                    @change="onSelectChange($event, inventoryFilters.bodyStyle.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.make.value"
                    :options="inventoryFilters.make.options"
                    @change="onSelectChange($event, inventoryFilters.make.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>
            </div>

            <div class="form-row">
              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.daysInStockFrom.value"
                    :options="inventoryFilters.daysInStockFrom.options"
                    @change="onSelectChange($event, inventoryFilters.daysInStockFrom.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.daysInStockTo.value"
                    :options="inventoryFilters.daysInStockTo.options"
                    @change="onSelectChange($event, inventoryFilters.daysInStockTo.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-input-group size="md" prepend="$">
                    <b-form-input
                      max='200'
                      v-model="inventoryFilters.priceFrom.value"
                      placeholder="Enter Price To">
                    </b-form-input>
                  </b-input-group>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-input-group size="md" prepend="$">
                    <b-form-input
                      max='200'
                      v-model="inventoryFilters.priceTo.value"
                      placeholder="Enter Price To">
                    </b-form-input>
                  </b-input-group>
                </b-form-group>
              </b-col>
            </div>

            <div class="form-row">
              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.mileageFrom.value"
                    :options="inventoryFilters.mileageFrom.options"
                    @change="onSelectChange($event, inventoryFilters.mileageFrom.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.mileageTo.value"
                    :options="inventoryFilters.mileageTo.options"
                    @change="onSelectChange($event, inventoryFilters.mileageTo.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <b-form-select
                    class="custom-size"
                    v-model="inventoryFilters.photoCount.value"
                    :options="inventoryFilters.photoCount.options"
                    @change="onSelectChange($event, inventoryFilters.photoCount.filterName)">
                  </b-form-select>
                </b-form-group>
              </b-col>

              <b-col lg="3">
                <b-form-group>
                  <div class="flex-fill">
                    <b-btn class="btn-block" variant="primary" type="submit">Search</b-btn>
                  </div>
                </b-form-group>
              </b-col>
            </div>
          </b-card-body>
        </b-form>
        </b-collapse>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
import inventoryFiltersMixin from './../../../mixins/craigslist/inventoryFiltersMixin'
import paging from './../../../components/_shared/paging.vue'
import defaultCraigslistInventoryFilters from './../../../shared/craigslist/inventoryFilters'
import lodash from 'lodash'

export default {
  name: 'inventory-filters',
  components: {
    'paging': paging
  },
  mixins: [inventoryFiltersMixin],
  props: {
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    refineSearchData: { type: Object, required: true }
  },
  data: function () {
    const inventoryFilters = this.buildInventoryFilters()

    return {
      inventoryFilters: inventoryFilters,
      isAdvancedFiltersVisible: this.isAdvantageSearchUnfolded(inventoryFilters)
    }
  },
  computed: {
    selectedCraigslistStatus: {
      get: function () {
        let index = this.inventoryFilters.craigslistStatus.options.findIndex(x => x.isActive, true)
        if (index >= 0) {
          return index
        }

        return 0
      },
      set: function (index) {
        const filters = lodash.assign({},
          defaultCraigslistInventoryFilters.objKeyValues,
          { [this.inventoryFilters.craigslistStatus.filterName]: this.inventoryFilters.craigslistStatus.options[index].value }
        )
        this.onFiltersChange(filters, false)
        this.isAdvancedFiltersVisible = false
      }
    }
  },
  watch: {
    refineSearchData: {
      deep: true,
      immediate: true,
      handler () {
        this.inventoryFilters = this.buildInventoryFilters()
      }
    }
  },
  methods: {
    buildInventoryFilters () {
      return {
        search: {
          filterName: 'search',
          value: this.refineSearchData.search.value
        },
        make: {
          filterName: 'make',
          options: this.getFilterOptions(this.refineSearchData.makes),
          value: this.getFilterValue(this.refineSearchData.makes)
        },
        condition: {
          filterName: 'condition',
          options: this.getFilterOptions(this.refineSearchData.conditions),
          value: this.getFilterValue(this.refineSearchData.conditions)
        },
        mileageFrom: {
          filterName: 'mileageFrom',
          options: this.getFilterOptions(this.refineSearchData.mileagesFrom),
          value: this.getFilterValue(this.refineSearchData.mileagesFrom)
        },
        mileageTo: {
          filterName: 'mileageTo',
          options: this.getFilterOptions(this.refineSearchData.mileagesTo),
          value: this.getFilterValue(this.refineSearchData.mileagesTo)
        },
        priceFrom: {
          filterName: 'priceFrom',
          value: this.refineSearchData.priceFrom.value
        },
        priceTo: {
          filterName: 'priceTo',
          value: this.refineSearchData.priceTo.value
        },
        daysInStockFrom: {
          filterName: 'daysFrom',
          options: this.getFilterOptions(this.refineSearchData.daysInStockFrom),
          value: this.getFilterValue(this.refineSearchData.daysInStockFrom)
        },
        daysInStockTo: {
          filterName: 'daysTo',
          options: this.getFilterOptions(this.refineSearchData.daysInStockTo),
          value: this.getFilterValue(this.refineSearchData.daysInStockTo)
        },
        photoCount: {
          filterName: 'photosFrom',
          options: this.getFilterOptions(this.refineSearchData.photoCount),
          value: this.getFilterValue(this.refineSearchData.photoCount)
        },
        craigslistStatus: {
          filterName: 'status',
          options: this.refineSearchData.craigslistStatuses
        },
        vehicleType: {
          filterName: 'vehicleType',
          options: this.getFilterOptions(this.refineSearchData.vehicleType),
          value: this.getFilterValue(this.refineSearchData.vehicleType)
        },
        bodyStyle: {
          filterName: 'bodyStyle',
          options: this.getFilterOptions(this.refineSearchData.bodyStyle),
          value: this.getFilterValue(this.refineSearchData.bodyStyle)
        }
      }
    },
    onFiltersChange (filterNameValues, hasToResetPageNumber = true) {
      if (hasToResetPageNumber) {
        filterNameValues['page'] = 1
      }
      this.$emit('filtersChanged', filterNameValues)
    },
    onSubmitSearch () {
      const filters = {}
      if (!this.isAdvancedFiltersVisible) {
        const defaultKeyValues = lodash.assign({}, defaultCraigslistInventoryFilters.objKeyValues)
        delete defaultKeyValues.status // to avoid resent current active tab
        lodash.assign(filters, defaultKeyValues)
      }
      lodash.assign(filters, {[this.inventoryFilters.search.filterName]: this.inventoryFilters.search.value})

      this.onFiltersChange(filters)
    },
    getFilterOptions (refineSearchDataFilterOptions) {
      if (!refineSearchDataFilterOptions) {
        return []
      }

      return refineSearchDataFilterOptions.map(x => {
        return {
          value: x.value,
          text: x.label
        }
      })
    },
    getFilterValue (refineSearchDataFilterOptions) {
      if (!refineSearchDataFilterOptions) {
        return null
      }

      const activeFilter = refineSearchDataFilterOptions.find(x => x.isActive, true)
      if (activeFilter) {
        return activeFilter.value
      }

      return null
    },
    onFilterApply () {
      this.onFiltersChange({
        [this.inventoryFilters.priceFrom.filterName]: this.inventoryFilters.priceFrom.value,
        [this.inventoryFilters.priceTo.filterName]: this.inventoryFilters.priceTo.value
      })
    },
    onSelectChange (value, filterName) {
      this.onFiltersChange({
        [filterName]: value
      })
    },
    isAdvantageSearchUnfolded (inventoryFilters) {
      return (inventoryFilters.condition.value && inventoryFilters.condition.value !== defaultCraigslistInventoryFilters.objKeyValues.condition) ||
        (inventoryFilters.vehicleType.value && inventoryFilters.vehicleType.value !== defaultCraigslistInventoryFilters.objKeyValues.vehicleType) ||
        (inventoryFilters.bodyStyle.value && inventoryFilters.bodyStyle.value !== defaultCraigslistInventoryFilters.objKeyValues.bodyStyle) ||
        (inventoryFilters.make.value && inventoryFilters.make.value !== defaultCraigslistInventoryFilters.objKeyValues.make) ||
        (inventoryFilters.daysInStockFrom.value && inventoryFilters.daysInStockFrom.value !== defaultCraigslistInventoryFilters.objKeyValues.daysFrom) ||
        (inventoryFilters.daysInStockTo.value && inventoryFilters.daysInStockTo.value !== defaultCraigslistInventoryFilters.objKeyValues.daysTo) ||
        (inventoryFilters.priceFrom.value && inventoryFilters.priceFrom.value !== defaultCraigslistInventoryFilters.objKeyValues.priceFrom) ||
        (inventoryFilters.priceTo.value && inventoryFilters.priceTo.value !== defaultCraigslistInventoryFilters.objKeyValues.priceTo) ||
        (inventoryFilters.mileageFrom.value && inventoryFilters.mileageFrom.value !== defaultCraigslistInventoryFilters.objKeyValues.mileageFrom) ||
        (inventoryFilters.mileageTo.value && inventoryFilters.mileageTo.value !== defaultCraigslistInventoryFilters.objKeyValues.mileageTo) ||
        (inventoryFilters.photoCount.value && inventoryFilters.photoCount.value !== defaultCraigslistInventoryFilters.objKeyValues.photosFrom)
    }
  }
}
</script>

<style lang="scss">
.custom-craiglist-inventory-paging {
  position: absolute;
  right: -10px;
  top: -15px;
  z-index: 2;
}
.collapsed > .when-opened,
:not(.collapsed) > .when-closed {
  display: none;
}
.toggle-search-filters-link {
  text-decoration: underline;
}
@media (max-width: 575px) {
  .nav-responsive-sm > .nav, .nav-responsive-sm > div > .nav {
    flex-wrap: nowrap!important;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    border: 0;
    overflow-x: scroll;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
  .nav-responsive-sm > div > .nav-tabs .nav-item {
    -webkit-box-flex: 1 !important;
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
}

</style>
