'use strict'
const merge = require('webpack-merge')
const config = require('../config')
const devWebpackConfigPromise = require('./webpack.dev.conf')

module.exports = devWebpackConfigPromise.then(
  (devWebpackConfig) => {
    const localWebpackConfig = merge(devWebpackConfig, {
      // these devServer options should be customized in /config/index.js
      devServer: {
        proxy: config.local.proxyTable,
        disableHostCheck: true
      },
    });

    return localWebpackConfig
  }
)
