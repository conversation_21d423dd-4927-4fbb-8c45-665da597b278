<template>
  <editSettingsHelper :readOnlyMode="readOnlyMode" @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Fuel Types" :isLoading="isUpdatingProcessed" :isDisabled="isDisabled" :isViewMode="isViewMode">
    <template slot="settings-content">
      <detail-row v-if="accountLevel" :fixed-payload-width="true" :title-position="'start'">
        <span slot="title">Use Custom Fuel Types Settings:</span>
        <b-form-checkbox v-if="!isViewMode" slot="payload" v-model="updatedSettings.hasToUseCustomFuelTypeSettings"></b-form-checkbox>
        <span v-else slot="payload">
          {{ updatedSettings.hasToUseCustomFuelTypeSettings ? 'Yes' : 'No' }}
        </span>
      </detail-row>
      <b-overlay :show="!(!accountLevel || updatedSettings.hasToUseCustomFuelTypeSettings)" opacity="0.7" blur="1.2px">
        <template #overlay>
          <span></span>
        </template>
        <ValidationProvider v-for="fuelType in updatedSettings.fuelTypeItems" :key="fuelType.fuelType"
        :name="getFuelTypeDesc(fuelType.fuelType)" :rules="getAccountFuelTypeRules(fuelType.fuelType)" v-slot="{errors}">
          <detail-row :fixed-payload-width="true" :title-position="'start'" :error="errors[0]">
            <span slot="title">{{ getFuelTypeDesc(fuelType.fuelType) }}</span>
            <span slot="payload" v-if="isViewMode">{{ fuelType.descriptionText || '-' }}</span>
            <b-form-input slot="payload" v-else v-model="fuelType.descriptionText"></b-form-input>
          </detail-row>
        </ValidationProvider>
      </b-overlay>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsMixin from '../editSettingsMixin'
import globals from '../../../../globals'
import { engineFuels } from '@/shared/inventory/vehicleTypes'

export default {
  name: 'tts-fuel-types-settings-section',
  props: {
    settings: { type: Object, required: true },
    isUpdatingProcessed: { type: Boolean, required: true },
    isDisabled: Boolean,
    accountLevel: Boolean,
    readOnlyMode: Boolean
  },
  data () {
    return {
      updatedSettings: globals().getClonedValue(this.settings),
      isViewMode: true
    }
  },
  mixins: [editSettingsMixin],
  components: {
    detailRow,
    editSettingsHelper
  },
  methods: {
    updateSettings () {
      this.$emit('saveChanges', this.updatedSettings)
    },
    getFuelTypeDesc (value) {
      let fuel = Object.values(engineFuels).find(x => x.value === value)
      return (fuel || {text: '-'}).text
    },
    getAccountFuelTypeRules (fuelType) {
      if (this.accountLevel && this.updatedSettings.hasToUseCustomFuelTypeSettings) {
        return fuelType !== engineFuels.notDefined.value ? 'required' : ''
      }
      return ''
    }
  },
  watch: {
    'updatedSettings': {
      deep: true,
      handler: function () {
        this.$emit('input', this.updatedSettings)
      }
    }
  }
}
</script>
