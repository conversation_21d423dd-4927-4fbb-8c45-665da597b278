<template>
  <details-section title="Engine" @cancel="onCancel" v-model="mode" v-if="truckAttributes">
    <div class="view" v-if="mode === 'view'">

      <auto-detail-row :title="truckFeatures.engineMaker.name" :text="getSelectedAttributeOption(truckFeatures.engineMaker).key"/>

      <auto-detail-row :title="truckFeatures.horsepower.name" :text="getSelectedAttributeOption(truckFeatures.horsepower).key"/>

      <auto-detail-row :title="truckFeatures.torque.name" :text="getSelectedAttributeOption(truckFeatures.torque).key"/>

      <auto-detail-row title="Engine Details" :text="vehicle.engine"/>

      <auto-detail-row :title="truckFeatures.engineBrake.name" :text="getSelectedAttributeOption(truckFeatures.engineBrake).key"/>

      <auto-detail-row title="Fuel Type" :text="getFuelType"/>

      <auto-detail-row title="Cylinders" :text="getCylinders"/>

      <auto-detail-row :title="truckFeatures.exhaustDetails.name" :text="getSelectedAttributeOption(truckFeatures.exhaustDetails).key"/>

      <auto-detail-row :title="truckFeatures.fuelTankDetails.name" :text="getSelectedAttributeOption(truckFeatures.fuelTankDetails).key"/>

    </div>

    <div class="edit" v-else-if="mode === 'edit'">

      <auto-detail-row :title="truckFeatures.engineMaker.name" v-model="truckFeatures.engineMaker.value" :options="getNameValueOptions(truckFeatures.engineMaker.nameValueOptions)"/>

      <auto-detail-row :title="truckFeatures.horsepower.name" v-model="truckFeatures.horsepower.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.torque.name" v-model="truckFeatures.torque.value" validation-rule="xml"/>

      <auto-detail-row title="Engine Details" v-model="vehicle.engine" validation-rule="max:50|xml"/>

      <auto-detail-row :title="truckFeatures.engineBrake.name" v-model="truckFeatures.engineBrake.value" :options="getNameValueOptions(truckFeatures.engineBrake.nameValueOptions)" />

      <auto-detail-row title="Fuel Type" v-model="vehicle.engineFuelId" :options="metadata.engineFuelOptions" validation-rule="xml"/>

      <auto-detail-row title="Cylinders" v-model="vehicle.cylindersType" :options="metadata.cylinderOptions" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.exhaustDetails.name" v-model="truckFeatures.exhaustDetails.value" validation-rule="xml"/>

      <auto-detail-row :title="truckFeatures.fuelTankDetails.name" v-model="truckFeatures.fuelTankDetails.value" validation-rule="xml"/>

    </div>
  </details-section>
</template>

<script>
import { mapGetters } from 'vuex'
import detailsSection from '@/components/details/detailsSection'
import autoDetailRow from '../helpers/autoDetailRow'
import featuresHelper from '../../../shared/details/featuresHelper'

export default {
  name: 'vehicle-history-section',
  data () {
    return {
      mode: 'view'
    }
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata', 'truckAttributes']),
    truckFeatures () {
      return {
        engineMaker: this.getFeatureById(-10089),
        horsepower: this.getFeatureById(-10090),
        torque: this.getFeatureById(-10092),
        engineBrake: this.getFeatureById(-10093),
        exhaustDetails: this.getFeatureById(-10091),
        fuelTankDetails: this.getFeatureById(-10094)
      }
    },
    getFuelType () {
      return this.metadata.engineFuelOptions[this.vehicle.engineFuelId] || '-'
    },
    getCylinders () {
      const cylinderType = this.vehicle.cylindersType
      const cylinders = this.metadata.cylinderOptions[cylinderType]
      return `${cylinders} ${!isNaN(cylinderType) ? ' Cylinders' : ''}`
    }
  },
  methods: {
    getFeatureById (id) {
      return featuresHelper.getFeatureById(this.truckAttributes, id)
    },
    getNameValueOptions (nameValueOptions) {
      return featuresHelper.getNameValueOptions(nameValueOptions)
    },
    getSelectedAttributeOption (attribute) {
      return featuresHelper.getSelectedAttributeOption(attribute)
    },
    onCancel () {
      this.$forceUpdate()
    }
  },
  components: {
    'details-section': detailsSection,
    'auto-detail-row': autoDetailRow
  }
}
</script>
