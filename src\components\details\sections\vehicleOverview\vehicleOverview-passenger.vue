<template>
  <div v-if="mode === 'view'">

    <auto-detail-row title="Style" :text="getVehicleStyle" titlePosition="start"/>

    <auto-detail-row title="Gallery Display Name" :text="vehicle.trim"/>

    <auto-detail-row title="Body Style" :text="getBodyStyle"/>

    <auto-detail-row title="Transmission" :text="`${getTransGearsDescription} ${getTransmissionDescription}`"/>

    <auto-detail-row title="Drivetrain" :text="getDrivetrainDescription"/>

    <auto-detail-row title="Engine" :text="getEngineDescription"/>

    <auto-detail-row title="Fuel Economy" :text="[getFuelEconomy,vehicle.miscellaneousVehicleDetails.hasToDisplayMpg ? 'Display On Website' : 'Doesn\'t Display On Website']"/>

  </div>
  <div v-else-if="mode === 'edit'">
    <ValidationProvider immediate name="Year" :rules="getYearValidateRules" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Year:</span>
      <custom-select slot="payload"
                     v-model="vehicle.year"
                     :customSelectValue="{selectVal: vehicle.year,inputVal: vehicle.year}"
                     name="year"
                     :options="getYearsOptions"
                     @change="onInputYear"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Make" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Make:</span>

      <custom-select slot="payload"
                     v-model="vehicle.make"
                     :customSelectValue="{selectVal: vehicle.make,inputVal: vehicle.make}"
                     :options="getMakesOptions"
                     @change="onInputMakes"
                     name="make"/>
    </detail-row>
    </ValidationProvider>

    <ValidationProvider immediate name="Model" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode mobileWrap :error="errors[0]">
      <span slot="title">Model:</span>
      <custom-select slot="payload"
                     v-model="vehicle.model"
                     :customSelectValue="{selectVal: vehicle.vehicleYearModelId,inputVal: vehicle.model}"
                     :options="getModelsOptions"
                     @change="onModelInputValueChanged"
                     name="model"/>
    </detail-row>
    </ValidationProvider>

    <detail-row  bigPayloadWidth editMode v-if="isModelSelected && getVehicleStyleOptions.length > 0" titlePosition="start">
      <span slot="title">Style:</span>
      <b-form-select
        slot="payload"
        :value="vehicle.vehicleStyleId"
        @input="onVehicleStyleInput"
        :options="getVehicleStyleOptions"
        :select-size="3"/>
    </detail-row>

    <auto-detail-row title="Gallery Display Name" v-model="vehicle.trim" validation-rule="max:100|xml"/>

    <auto-detail-row v-if="isModelSelected" title="Body Style" :value="vehicle.bodyStyleId" @input="onBodyStyleInput" :options="metadata.bodyStyleOptions"/>

    <detail-row fixedPayloadWidth editMode v-if="isModelSelected">
      <span slot="title">Transmission:</span>
      <div slot="payload" class="d-flex" style="flex-grow: 1">
        <b-form-select :value="vehicle.transGearsId" @input="onTransGearsInput" :options="metadata.transmissionGearsOptions"></b-form-select>
        <span class="px-2"></span>
        <b-form-select :value="vehicle.transTypeId" @input="onTransTypeInput" :options="metadata.transmissionOptions"></b-form-select>
      </div>
    </detail-row>

    <auto-detail-row v-if="isModelSelected" title="Drivetrain" :value="vehicle.drivetrainId" @input="onDrivetrainInput" :options="metadata.drivetrainOptions"/>

    <ValidationProvider immediate name="Engine" rules="required|max:50|xml" v-slot="{errors}">
    <detail-row editMode v-if="isModelSelected" mobileWrap :error="errors[0]">
      <span slot="title">Engine:</span>
      <custom-select slot="payload"
                     v-model="vehicle.engine"
                     :customSelectValue="{inputVal: vehicle.engine,selectVal: vehicle.engine}"
                     :options="getVehicleEngine"
                     @change="onInputEngine"
                     name="engine"/>
    </detail-row>
    </ValidationProvider>

    <auto-detail-row v-if="isModelSelected" title="No. Of Cylinders" :value="vehicle.cylindersType" @input="onCylindersInput" :options="metadata.cylinderOptions" validation-rule="max:2"/>

    <auto-detail-row v-if="isModelSelected" title="Fuel Type" :value="vehicle.engineFuelId" @input="onFuelInput" :options="metadata.engineFuelOptions"/>

    <detail-row fixedPayloadWidth editMode>
      <span slot="title">Fuel Economy:</span>
      <div slot="payload" class="d-flex spacing-nowrap">
        <span class="align-self-center pl-2">{{getFuelEconomy}}</span>
        <span class="pl-3 pr-3 align-self-center">|</span>
        <b-form-checkbox v-model="vehicle.miscellaneousVehicleDetails.hasToDisplayMpg"> {{vehicle.miscellaneousVehicleDetails.hasToDisplayMpg ? 'Display On Website' : 'Doesn\'t Display On Website'}} </b-form-checkbox>
      </div>
    </detail-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import detailRow from '../../helpers/detailRow'
import splitHelper from '../../helpers/spliterHelper'
import selectWithCustomValue from '../../helpers/selectWithCustomValue'
import VehicleOverviewHelper from '@/shared/details/vehicleOverviewHelper'
import autoDetailRow from '../../helpers/autoDetailRow'

let overviewHelper = null

export default {
  name: 'passenger',
  props: {
    mode: String
  },
  created () {
    overviewHelper = new VehicleOverviewHelper(this.vehicle, this.$store, this.$logger)
  },
  computed: {
    ...mapGetters('details', ['vehicle', 'metadata']),
    ...mapGetters('categoryData', ['makes', 'models', 'years', 'styles', 'engines']),

    getBodyStyle () {
      return this.metadata.bodyStyleOptions[this.vehicle.bodyStyleId] || ''
    },
    getTransmissionDescription () {
      return this.metadata.transmissionOptions[this.vehicle.transTypeId] || ''
    },
    getTransGearsDescription () {
      return this.metadata.transmissionGearsOptions[this.vehicle.transGearsId] || ''
    },
    getDrivetrainDescription () {
      return this.metadata.drivetrainOptions[this.vehicle.drivetrainId] || ''
    },
    isModelSelected () {
      return !!this.vehicle.model
    },
    getYearsOptions () {
      return this.years.map(x => ({
        value: x,
        text: x
      }))
    },
    getMakesOptions () {
      return this.makes.map(x => ({
        value: x,
        text: x
      }))
    },
    getModelsOptions () {
      return this.models.map(x => ({
        value: x.vehicleYearModelId,
        text: x.modelName
      }))
    },
    getVehicleStyleOptions () {
      return this.styles.map(x => ({
        value: x.vehicleStyleId,
        text: `${x.galleryStyleName} (${x.vehicleStyleId})`
      }))
    },
    getVehicleStyle () {
      let res = []
      let style = this.styles.find(x => x.vehicleStyleId === this.vehicle.vehicleStyleId)
      if (style) {
        res.push(`${style.vehicleStyleName}`)
      }

      if (this.vehicle.vehicleStyleId !== undefined) {
        res.push(`Style ID: ${this.vehicle.vehicleStyleId}`)
      }

      if (this.vehicle.manufacturerModelCode) {
        res.push(`OEM ModelCode: ${this.vehicle.manufacturerModelCode}`)
      }

      return res
    },
    getEngineDescription () {
      let cylinderType = this.vehicle.cylindersType
      let engineDesc = this.vehicle.engine
      let cylinders = this.metadata.cylinderOptions[cylinderType]
      let engineFuel = this.metadata.engineFuelOptions[this.vehicle.engineFuelId]
      return [engineDesc, `${cylinders} ${!isNaN(cylinderType) ? ' Cylinders' : ''}`, engineFuel]
    },
    getFuelEconomy () {
      return `${this.vehicle.miscellaneousVehicleDetails.cityFuelEconomyValue} City / ${this.vehicle.miscellaneousVehicleDetails.hwyFuelEconomyValue} Highway`
    },
    getVehicleEngine () {
      return this.engines.map(x => ({
        value: x,
        text: x
      }))
    },

    getYearValidateRules () {
      let currentYear = new Date().getFullYear()
      return `required|between:${currentYear - 100},${currentYear + 1}`
    }
  },
  methods: {
    onInputYear (newVal) {
      overviewHelper.updateYear(newVal.isInputMode ? newVal.text : newVal.value)
    },
    onInputMakes (newVal) {
      overviewHelper.updateVehicleMake(newVal.isInputMode ? newVal.text : newVal.value)
    },
    onModelInputValueChanged (newVal) {
      overviewHelper.updateVehicleModel(newVal.text)
    },
    onVehicleStyleInput (styleId) {
      let style = this.styles.find(x => x.vehicleStyleId === styleId)
      overviewHelper.updateVehicleStyle(style)
    },
    onBodyStyleInput (newBodyStyle) {
      overviewHelper.updateBodyStyle(newBodyStyle)
    },
    onInputEngine (newEngine) {
      overviewHelper.updateEngine(newEngine.text)
    },
    onTransTypeInput (newTransTypeId) {
      overviewHelper.updateTransType(newTransTypeId)
    },
    onTransGearsInput (newTransGearId) {
      overviewHelper.updateTransGears(newTransGearId)
    },
    onDrivetrainInput (newDrivetrainId) {
      overviewHelper.updateDrivetrain(newDrivetrainId)
    },
    onCylindersInput (newCylinderType) {
      overviewHelper.updateCylinders(newCylinderType)
    },
    onFuelInput (newFuel) {
      overviewHelper.updateFuel(newFuel)
    }
  },
  watch: {
    vehicle () {
      overviewHelper = new VehicleOverviewHelper(this.vehicle, this.$store, this.$logger)
    }
  },
  components: {
    'custom-select': selectWithCustomValue,
    'detail-row': detailRow,
    'split-helper': splitHelper,
    'auto-detail-row': autoDetailRow
  }
}
</script>

<style>
  .spacing-nowrap{
    white-space: nowrap;
  }
</style>
