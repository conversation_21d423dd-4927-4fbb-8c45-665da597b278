<template>
  <editSettingsHelper @cancel="cancel" @save="saveSettings" @changeMode="changeMode" title="Fixed Price Listings" :isLoading="isUpdatingProcessed" :isViewMode="isViewMode">
    <template slot="settings-content">
      <ValidationProvider name="Listing Quantity" rules="max_value:100|min_value:0" v-slot="{errors}">
      <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Listing Quantity:</span>
        <div slot="payload" class="d-flex flex-row w-100">
          <b-form-input v-if="!isViewMode" name="Listing_Quantity" v-model="settingsToUpdate.ListingQuantityInPercent" @blur.native="calculateListingQuantity" class="custom-input-width" type="number"></b-form-input>
          <span v-else>{{settings.ListingQuantityInPercent}}</span>
          <span class="ml-2 align-self-center custom-text-nowrap">% of all Listings are Auctions.</span>
        </div>
      </detail-row>
      </ValidationProvider>
      <detail-row :large-payload-width="true">
        <span slot="title">Listing Duration:</span>
        <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.ListingDurationInDays" slot="payload" :options="getDurationOptions"></b-form-select>
        <span v-else slot="payload">{{settings.ListingDurationInDays}} days</span>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Best Offers:</span>
        <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.HasToAllowedBestOffer" slot="payload" :options="getBestOfferOptions"></b-form-select>
        <span v-else slot="payload">{{getBestOfferDesc}}</span>
      </detail-row>
      <detail-row titlePosition="start" :large-payload-width="true">
        <span slot="title">Allow Best Offer Auto Decline:</span>
        <b-form-group
          slot="payload"
        >
          <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.IsAllowedBestOfferAutoDecline" :options="getAllowBestOfferAutoDeclineOptions"></b-form-select>
          <span v-else>{{getAllowBestOfferAutoDeclineDesc}}</span>
          <b-form-text class="text-dark custom-text-nowrap">
            This setting determines if you can set an Auto Decline amount for Best Offers on Fixed Priced Listings on the "Schedule"
          </b-form-text>
        </b-form-group>
      </detail-row>
      <ValidationProvider name="Auto Decline Threshold" rules="max_value:100" v-slot="{errors}">
      <detail-row title-position="start"  :large-payload-width="true" :error="errors[0]">
        <span slot="title">Auto Decline Threshold(%):</span>
        <div class="w-100" slot="payload">
          <b-form-input v-if="!isViewMode" name="Auto_Decline_Threshold" min="0" type="number" v-model="settingsToUpdate.AutoDeclineThresholdInPercent"></b-form-input>
          <span v-else>{{settings.AutoDeclineThresholdInPercent}}%</span>
        </div>
      </detail-row>
      </ValidationProvider>
      <ValidationProvider name="Buy It Now Price" rules="max_value:100|min_value:0" v-slot="{errors}">
      <detail-row title-position="start" :large-payload-width="true" :error="errors[0]">
        <span slot="title">Buy It Now Price:</span>
        <div slot="payload" class="d-flex flex-row w-100">
          <b-form-input v-if="!isViewMode" name="Buy_It_Now_Price" v-model="settingsToUpdate.BuyItNowPriceInPercent" class="custom-input-width" type="number"></b-form-input>
          <span v-else>{{settings.BuyItNowPriceInPercent}}</span>
          <span class="ml-2 align-self-center custom-text-nowrap">% of the lowest price.</span>
        </div>
      </detail-row>
      </ValidationProvider>
      <detail-row :large-payload-width="true">
        <span slot="title">New Vehicle Pricing Logic:</span>
        <b-form-select v-if="!isViewMode" v-model="settingsToUpdate.NewVehiclesPriceType" slot="payload" :options="getPriceLogicOptions"></b-form-select>
        <span slot="payload" v-else>{{getPricingDesc(settings.NewVehiclesPriceType)}}</span>
      </detail-row>
      <detail-row :large-payload-width="true">
        <span slot="title">Used Vehicle Pricing Logic:</span>
        <b-form-select v-if="!isViewMode" slot="payload" v-model="settingsToUpdate.UsedVehiclesPriceType" :options="getPriceLogicOptions"></b-form-select>
        <span slot="payload" v-else>{{getPricingDesc(settings.UsedVehiclesPriceType)}}</span>
      </detail-row>
    </template>
  </editSettingsHelper>
</template>

<script>
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'
import ebayOptions from '@/shared/ebay/ebayOptions'
import globals from '../../../globals'
import editSettingsMixin from '@/mixins/eBay/editSettingsMixin'

export default {
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isUpdatingProcessed: false,
      isViewMode: true,
      settingsToUpdate: globals().getClonedValue(this.settings)
    }
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  computed: {
    getDurationOptions () {
      return ebayOptions.automatedDurationOptions
    },
    getBestOfferOptions () {
      return ebayOptions.bestOfferOptions
    },
    getAllowBestOfferAutoDeclineOptions () {
      return ebayOptions.yesNoOptions
    },
    getPriceLogicOptions () {
      return ebayOptions.priceLogicOptions
    },
    getBestOfferDesc () {
      return (ebayOptions.bestOfferOptions.find(x => x.value === this.settings.HasToAllowedBestOffer) || {text: '-'}).text
    },
    getAllowBestOfferAutoDeclineDesc () {
      return (ebayOptions.yesNoOptions.find(x => x.value === this.settings.IsAllowedBestOfferAutoDecline) || {text: '-'}).text
    }
  },
  mixins: [editSettingsMixin],
  methods: {
    updateSettings () {
      this.isUpdatingProcessed = true
      this.$emit('update', this.settingsToUpdate)
      this.isUpdatingProcessed = false
      this.isViewMode = true
    },
    getPricingDesc (priceType) {
      return (ebayOptions.priceLogicOptions.find(x => x.value === priceType) || {text: '-'}).text
    },
    copySettings () {
      this.settingsToUpdate = globals().getClonedValue(this.settings)
    },
    calculateListingQuantity () {
      this.$emit('calculateListingQuantity', +this.settingsToUpdate.ListingQuantityInPercent)
    }
  },
  watch: {
    'settings': {
      deep: true,
      handler: function () {
        this.copySettings()
      }
    }
  }
}
</script>

<style scoped>
@media (min-width: 1320px) {
  .custom-text-nowrap {
    white-space: nowrap;
  }
}

.custom-input-width {
  width: 5rem;
}
</style>
