<template>
  <b-card no-body class="w-100 mb-3 mb-lg-0">
    <b-card-header header-tag="h5" class="with-elements border-0 pl-3">
      <div class="card-header-title">Traffic Sources</div>
      <div class="card-header-elements ml-auto">
        <analytics-router-link :to="trafficSourcesReportLinkName" class="btn d-block btn-outline-secondary btn-sm">Details <i class="ion ion-ios-arrow-forward" /></analytics-router-link>
      </div>
    </b-card-header>
    <div>
      <vue-echart :options="trafficSourcesTimelineOptions" :auto-resize="true"></vue-echart>
    </div>
  </b-card>
</template>

<script>
import dateHelper from '@/plugins/locale/date'

import 'echarts/lib/chart/line'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import VueEchart from 'vue-echarts/components/ECharts.vue'
import AnalyticsRouterLink from '../analyticsRouterLink'

const colors = ['#dc3545', '#28a745', '#007bff', '#ffc107', '#7751bd', '#a7b61a', '#f3e562', '#ff9800', '#ff5722', '#ff4514', '#647c8a', '#3f51b5', '#2196f3', '#00b862', '#afdf0a']

export default {
  name: 'dashboard-traffic-sources-graph',
  props: {
    trafficSourcesTimelineItems: { type: Array, required: true },
    accountId: Number,
    reportGroupId: String
  },
  components: {
    AnalyticsRouterLink,
    VueEchart
  },
  computed: {
    trafficSourcesReportLinkName () {
      if (this.accountId) {
        return { name: 'ga4TrafficSources', params: { accountId: this.accountId } }
      } else if (this.reportGroupId) {
        return { name: 'ga4GroupTrafficSources', params: { reportGroupId: this.reportGroupId } }
      }
      return ''
    },
    trafficSourcesTimelineOptions () {
      return {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let tooltip = params[0].name

            params.forEach(x => {
              tooltip += '<br />' +
                  `${x.marker}${x.seriesName}: ${this.$locale.formatNumber(x.value)}`
            })

            return tooltip
          }
        },
        color: colors,
        legend: {
          data: this.trafficSourcesTimelineLabels,
          show: false,
          top: '10',
          orient: 'vertical'
        },
        grid: {
          left: '50px',
          top: '20px',
          right: '0',
          bottom: '30px'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.trafficSourcesTimelineDates,
          axisLine: {
            show: false,
            lineStyle: { color: 'rgba(0, 0, 0, .5)' }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          boundaryGap: false,
          show: true,
          axisLine: {
            show: false,
            lineStyle: { color: 'rgba(0, 0, 0, .5)' }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 50,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        areaStyle: { },
        series: this.trafficSourcesTimelineLines,
        animationDuration: 2000
      }
    },
    trafficSourcesTimelineLabels () {
      return this.trafficSourcesTimelineItems.map(
        x => x.channelGrouping ? `${x.source} (${x.channelGrouping})` : x.source
      )
    },
    trafficSourcesTimelineDates () {
      if (this.trafficSourcesTimelineItems.length === 0) {
        return []
      }

      return this.trafficSourcesTimelineItems[0].items.map(x => {
        return dateHelper.getDayFormatted(x.dateFrom, this.barTimeFormat)
      })
    },
    trafficSourcesTimelineLines () {
      return this.trafficSourcesTimelineItems.map(src => {
        return {
          name: src.channelGrouping ? `${src.source} (${src.channelGrouping})` : src.source,
          type: 'line',
          stack: 'referrals',
          data: src.items.map(stat => stat.sessions),
          areaStyle: {},
          smooth: 0.4,
          symbolSize: 7,
          showSymbol: false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
