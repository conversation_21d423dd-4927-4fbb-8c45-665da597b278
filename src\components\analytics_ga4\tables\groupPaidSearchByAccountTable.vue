<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
    @accountNameClicked="onAccountNameClicked"
  >
    <template slot="row-details" slot-scope="{ item }">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Form Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.formLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>SMS Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.smsLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Phone Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.phoneLeads) }}</b-col>
        </b-row>
        <b-button size="sm" @click="item.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'

export default {
  name: 'group-paid-search-by-account-table',
  props: {
    tableItems: {type: Array, required: true},
    totalItems: {type: Number, require: true},
    pageNumber: {type: Number, required: true},
    pageSize: {type: Number, required: true},
    sortType: {type: Number, required: true}
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'account',
          label: 'Account Name',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.accountNameAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.accountNameDesc
        },
        {
          key: 'spend',
          label: 'Spend',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.spendAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.spendDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'impressions',
          label: 'Impressions',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.impressionsAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.impressionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'clicks',
          label: 'Clicks',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.clicksAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.clicksDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'costPerClick',
          label: 'Cost Per Click',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.CPCAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.CPCDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'clickThroughRate',
          label: 'Click Through Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.CTRAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.CTRDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'sessions',
          label: 'Sessions',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.sessionsAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.sessionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'totalLeads',
          label: 'Total Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.totalLeadsAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.totalLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'conversionRate',
          label: 'Conversion Rate',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.convRateAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.convRateDesc,
          formatter: val => `${val}%`
        },
        {
          key: 'costPerLead',
          label: 'Cost Per Lead',
          sortable: true,
          sortTypeAsc: analyticsConstants.paidSearchSortTypes.CPLAsc,
          sortTypeDesc: analyticsConstants.paidSearchSortTypes.CPLDesc,
          formatter: val => this.$locale.formatCurrency(val)
        },
        {
          key: 'show_details'
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  },
  methods: {
    onAccountNameClicked (account) {
      this.$emit('accountNameClicked', account)
    }
  }
}
</script>

<style scoped>

</style>
