<template>
  <editSettingsHelper v-if='isReady' title="Posting Limits Settings" @save="saveSettings" @cancel="cancel" @changeMode="changeMode" :isDisabled="isDisabled" :isLoading="isUpdatingProcessing" :isViewMode="isViewMode">
    <div slot="settings-content">
      <detail-row :title-position="'start'" :fixed-payload-width="true" :error="errors.maxCitiesVehicleCanBePostedTo">
        <span slot="title">Max Cities Vehicle Can Be Posted To:</span>
        <span v-if="isViewMode" slot="payload">{{ postingLimitsSettings.maxCitiesVehicleCanBePostedTo }}</span>
        <b-form-input v-else v-model="postingLimitsSettings.maxCitiesVehicleCanBePostedTo" @input="resetError('maxCitiesVehicleCanBePostedTo')" slot="payload" type="number"></b-form-input>
      </detail-row>
      <detail-row
        :title-position="'start'"
        :fixed-payload-width="true"
        :error="errors.maxVehiclePostsPerDay">
        <span slot="title">Max Vehicle Posts Per Day:</span>
        <span v-if="isViewMode" slot="payload">{{ postingLimitsSettings.maxVehiclePostsPerDay }}</span>
        <b-form-input v-else v-model="postingLimitsSettings.maxVehiclePostsPerDay" @input="resetError('maxVehiclePostsPerDay')" slot="payload" type="number"></b-form-input>
      </detail-row>
      <detail-row
        :title-position="'start'"
        :fixed-payload-width="true"
        :error="errors.maxAccountPostsPerDay">
        <span slot="title">Max Account Posts Per Day:</span>
        <span v-if="isViewMode" slot="payload">{{ postingLimitsSettings.maxAccountPostsPerDay }}</span>
        <b-form-input v-else v-model="postingLimitsSettings.maxAccountPostsPerDay" @input="resetError('maxAccountPostsPerDay')" slot="payload" type="number"></b-form-input>
      </detail-row>
      <detail-row :title-position="'start'" :fixed-payload-width="true"
      :error="errors.maxAccountPostsPerMonth">
        <span slot="title">Max Account Posts Per Month:</span>
        <span v-if="isViewMode" slot="payload">{{ postingLimitsSettings.maxAccountPostsPerMonth }}</span>
        <b-form-input v-else v-model="postingLimitsSettings.maxAccountPostsPerMonth" @input="resetError('maxAccountPostsPerMonth')" slot="payload" type="number"></b-form-input>
      </detail-row>
    </div>
  </editSettingsHelper>
</template>

<script>
import { mapGetters } from 'vuex'
import globals from '../../../globals'
import editSettingsHelper from '../../_shared/editSettingsHelper.vue'
import detailRow from '@/components/details/helpers/detailRow'

export default {
  name: 'section-admin-settings',
  props: {
    isDisabled: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      isReady: false,
      isViewMode: true,
      isUpdatingProcessing: false,
      accountId: +this.$route.params.accountId,
      postingLimitsSettings: {},
      errors: {}
    }
  },
  created () {
    this.populateData()
  },
  components: {
    editSettingsHelper,
    detailRow
  },
  computed: {
    ...mapGetters('craigslistSettings', ['settingsPutData'])
  },
  methods: {
    changeMode (newMode) {
      this.isViewMode = newMode
    },
    saveSettings () {
      this.putPostingLimitsSettingsData()
    },
    cancel () {
      this.postingLimitsSettings = Object.assign({}, globals().getClonedValue(this.settingsPutData.postingLimits))
      this.isViewMode = true
    },
    populateData () {
      this.postingLimitsSettings = globals().getClonedValue(this.settingsPutData.postingLimits)
      this.isReady = true
    },
    useCustomErrorMessageOnFailedRule (failedRules, ruleName, customMessage) {
      if (!failedRules) {
        return null
      }
      if (failedRules[ruleName]) {
        return customMessage
      }
      return Object.values(failedRules)[0]
    },
    putPostingLimitsSettingsData () {
      this.isUpdatingProcessing = true
      this.errors = Object.assign({}, {})
      const parameters = { accountId: this.accountId, data: { ...this.postingLimitsSettings, accountId: this.accountId } }
      this.$store.dispatch('craigslistSettings/putPostingLimitSettingsData', parameters).then(x => {
        this.$toaster.success('Posting Limits Settings Successfully Updated')
        this.$store.commit('craigslistSettings/setPostingLimitsSettings', this.postingLimitsSettings)
        this.isViewMode = true
      })
        .catch(ex => {
          if (ex.response.status === 400) {
            this.parseExceptionResponse(ex.response)
          } else {
            this.$toaster.error('Something went wrong!')
            this.$logger.handleError(ex, 'Error occurred on updating Craigslist posting limits settings')
          }
        })
        .finally(() => {
          this.isUpdatingProcessing = false
        })
    },
    parseExceptionResponse (response) {
      if (typeof response.data === 'string') {
        this.$toaster.error(`Something went wrong. Message: ${response.data}`, {timeout: 5000})
        return
      }
      if (response.data && response.data.model && response.data.model.validationErrors) {
        this.errors = Object.assign({}, response.data.model.validationErrors)
      }
    },
    resetError (key) {
      if (this.errors[key]) {
        this.$set(this.errors, key, '')
      }
    }
  }
}
</script>
