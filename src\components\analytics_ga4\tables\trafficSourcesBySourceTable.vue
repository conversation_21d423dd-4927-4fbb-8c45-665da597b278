<template>
  <common-analytics-table
    :tableItems="tableItems"
    :totalItems="totalItems"
    :tableFields="tableFields"
    :isPaginated="true"
    :sortType.sync="sortTypeProp"
    :pageNumber.sync="pageNumberProp"
    :pageSize.sync="pageSizeProp"
  >
    <template slot="row-details" slot-scope="{ item }">
      <b-card>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>New Sessions:</b></b-col>
          <b-col col>{{ item.item.percentNewSessions }}%</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Page Views Per Session:</b></b-col>
          <b-col col>{{ item.item.pageViewsPerSession }}%</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Time on Site:</b></b-col>
          <b-col>{{ $locale.getSecondsDurationFormatted(item.item.sessionDuration) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Form Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.formLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>SMS Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.smsLeads) }}</b-col>
        </b-row>
        <b-row class="mb-2">
          <b-col sm="12" md="4" lg="3" xl="2"><b>Phone Leads:</b></b-col>
          <b-col>{{ $locale.formatNumber(item.item.phoneLeads) }}</b-col>
        </b-row>
        <b-button size="sm" @click="item.toggleDetails">Hide Details</b-button>
      </b-card>
    </template>
  </common-analytics-table>
</template>

<script>
import analyticsConstants from '../../../shared/analytics/constants'
export default {
  name: 'traffic-sources-by-source-table',
  props: {
    tableItems: { type: Array, required: true },
    totalItems: { type: Number, require: true },
    pageNumber: { type: Number, required: true },
    pageSize: { type: Number, required: true },
    sortType: { type: Number, required: true }
  },
  components: {
    'common-analytics-table': () => import('./commonAnalyticsTable.vue')
  },
  computed: {
    tableFields () {
      return [
        {
          key: 'source',
          label: 'Source',
          sortable: true,
          sortTypeAsc: analyticsConstants.trafficSourceSortTypes.sourceAsc,
          sortTypeDesc: analyticsConstants.trafficSourceSortTypes.sourceDesc
        },
        {
          key: 'channelGrouping',
          label: 'Channel',
          sortable: true,
          sortTypeAsc: analyticsConstants.trafficSourceSortTypes.channelGroupingAsc,
          sortTypeDesc: analyticsConstants.trafficSourceSortTypes.channelGroupingDesc
        },
        {
          key: 'sessions',
          label: 'Sessions',
          sortable: true,
          sortTypeAsc: analyticsConstants.trafficSourceSortTypes.sessionsAsc,
          sortTypeDesc: analyticsConstants.trafficSourceSortTypes.sessionsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'pageViews',
          label: 'Page Views',
          sortable: true,
          sortTypeAsc: analyticsConstants.trafficSourceSortTypes.pageViewsAsc,
          sortTypeDesc: analyticsConstants.trafficSourceSortTypes.pageViewsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'avgSessionDuration',
          label: 'Avg. Time on Site',
          sortable: true,
          sortTypeAsc: analyticsConstants.trafficSourceSortTypes.avgTimeOnSiteAsc,
          sortTypeDesc: analyticsConstants.trafficSourceSortTypes.avgTimeOnSiteDesc,
          formatter: val => this.$locale.getSecondsDurationFormatted(val)
        },
        {
          key: 'totalLeads',
          label: 'Total Leads',
          sortable: true,
          sortTypeAsc: analyticsConstants.trafficSourceSortTypes.totalLeadsAsc,
          sortTypeDesc: analyticsConstants.trafficSourceSortTypes.totalLeadsDesc,
          formatter: val => this.$locale.formatNumber(val)
        },
        {
          key: 'show_details'
        }
      ]
    },
    sortTypeProp: {
      get () {
        return this.sortType
      },
      set (newVal) {
        this.$emit('sortTypeChanged', newVal)
      }
    },
    pageNumberProp: {
      get () {
        return this.pageNumber
      },
      set (newVal) {
        this.$emit('pageNumberChanged', newVal)
      }
    },
    pageSizeProp: {
      get () {
        return this.pageSize
      },
      set (newVal) {
        this.$emit('pageSizeChanged', newVal)
      }
    }
  }
}
</script>

<style scoped>

</style>
